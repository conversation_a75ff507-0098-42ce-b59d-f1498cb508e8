{"cells": [{"cell_type": "code", "execution_count": 106, "id": "00961034", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "import pandas as pd\n", "from sklearn.model_selection import train_test_split\n", "\n", "%matplotlib inline"]}, {"cell_type": "markdown", "id": "1eea030e", "metadata": {}, "source": ["### 1. Dataset"]}, {"cell_type": "code", "execution_count": 107, "id": "a3a91f62", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>age</th>\n", "      <th>physical_score</th>\n", "      <th>test_result</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>33.0</td>\n", "      <td>40.7</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>50.0</td>\n", "      <td>37.2</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>52.0</td>\n", "      <td>24.7</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>56.0</td>\n", "      <td>31.0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>35.0</td>\n", "      <td>42.9</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4995</th>\n", "      <td>73.0</td>\n", "      <td>3.9</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4996</th>\n", "      <td>57.0</td>\n", "      <td>33.9</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4997</th>\n", "      <td>49.0</td>\n", "      <td>34.5</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4998</th>\n", "      <td>38.0</td>\n", "      <td>46.4</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4999</th>\n", "      <td>48.0</td>\n", "      <td>38.5</td>\n", "      <td>1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5000 rows × 3 columns</p>\n", "</div>"], "text/plain": ["       age  physical_score  test_result\n", "0     33.0            40.7            1\n", "1     50.0            37.2            1\n", "2     52.0            24.7            0\n", "3     56.0            31.0            0\n", "4     35.0            42.9            1\n", "...    ...             ...          ...\n", "4995  73.0             3.9            0\n", "4996  57.0            33.9            1\n", "4997  49.0            34.5            1\n", "4998  38.0            46.4            1\n", "4999  48.0            38.5            1\n", "\n", "[5000 rows x 3 columns]"]}, "execution_count": 107, "metadata": {}, "output_type": "execute_result"}], "source": ["dataset = pd.read_csv(\"hearing_test.csv\")\n", "dataset"]}, {"cell_type": "code", "execution_count": 108, "id": "61522175", "metadata": {}, "outputs": [], "source": ["X = dataset[['age', 'physical_score']].values\n", "y = dataset[['test_result']].values"]}, {"cell_type": "code", "execution_count": 109, "id": "c0fb5232", "metadata": {"scrolled": true}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["dataset.plot.scatter(x='age',y='physical_score', c='test_result', colormap='winter')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 110, "id": "a6af73e0-8061-47a0-8045-e36f8eb565a6", "metadata": {}, "outputs": [], "source": ["# <PERSON><PERSON><PERSON>er les données en ensemble d'entraînement et de test\n", "X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=13)\n"]}, {"cell_type": "markdown", "id": "e3dc2093", "metadata": {}, "source": ["### 2. <PERSON><PERSON><PERSON><PERSON>\n"]}, {"cell_type": "code", "execution_count": 111, "id": "1ab1c3f2", "metadata": {}, "outputs": [], "source": ["# La fonction sigmoide\n", "def sigmoid(x):\n", "    return 1 / (1 + np.exp(-x))"]}, {"cell_type": "code", "execution_count": 112, "id": "e91bd550", "metadata": {}, "outputs": [], "source": ["def initialisation(X):\n", "    W = np.random.randn(X.shape[1], 1)\n", "    b = np.random.randn(1)\n", "    return (W, b)"]}, {"cell_type": "code", "execution_count": 113, "id": "c0eea391", "metadata": {}, "outputs": [], "source": ["# <PERSON><PERSON><PERSON><PERSON>\n", "def modele(X, W, b):\n", "    Z = X.dot(W) + b\n", "    A = sigmoid(Z)\n", "    return A"]}, {"cell_type": "markdown", "id": "92dc2207", "metadata": {}, "source": ["W,b=initialisation(X)\n", "modele(X, W, b)"]}, {"cell_type": "markdown", "id": "49599deb", "metadata": {}, "source": ["### 3. <PERSON><PERSON><PERSON> "]}, {"cell_type": "code", "execution_count": 114, "id": "b01a2e59", "metadata": {}, "outputs": [], "source": ["def log_loss(y, A):\n", "    return 1/len(y) * np.sum(-y * np.log(A) - (1 - y) * np.log(1 - A))"]}, {"cell_type": "markdown", "id": "4d2bcd83", "metadata": {}, "source": ["### 4. Optimisation - Gradient et Descente de Gradient"]}, {"cell_type": "code", "execution_count": 115, "id": "78ddbff0", "metadata": {}, "outputs": [], "source": ["def gradients(X, A, y):\n", "    dW = 1/len(y) * np.dot(X.T, A - y)\n", "    db = 1/len(y) * np.sum(A - y)\n", "    return (dW, db)"]}, {"cell_type": "code", "execution_count": 116, "id": "8de052c9", "metadata": {}, "outputs": [], "source": ["def optimisation(X, W, b, A, y, learning_rate):\n", "    dW, db = gradients(X, A, y)\n", "    W = W - learning_rate * dW\n", "    b = b - learning_rate * db\n", "    return (W, b)"]}, {"cell_type": "markdown", "id": "a5c6e0e2", "metadata": {}, "source": ["### 5. <PERSON><PERSON><PERSON>"]}, {"cell_type": "code", "execution_count": 117, "id": "f4b7fbed", "metadata": {}, "outputs": [], "source": ["def predict(X, W, b):\n", "    A = modele(X, W, b)\n", "    print(A)\n", "    return A >= 0.5"]}, {"cell_type": "code", "execution_count": null, "id": "ab882c65", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "b8cefe90", "metadata": {}, "source": ["### 5. <PERSON><PERSON><PERSON><PERSON> final"]}, {"cell_type": "code", "execution_count": 118, "id": "6f61fb2d", "metadata": {}, "outputs": [], "source": ["def regression_logistique(X, y, learning_rate=0.001, n_iter=10000):\n", "  # Initialisation\n", "    W, b = initialisation(X)\n", "    loss_history = []\n", "  # Entrainement\n", "    for i in range(n_iter):\n", "        A = modele(X, W, b)\n", "        loss_history.append(log_loss(y, A))\n", "        W, b = optimisation(X, W, b, A, y, learning_rate=0.001)\n", "\n", "  # Prediction\n", "    plt.plot(loss_history)\n", "    plt.xlabel('n_iteration')\n", "    plt.ylabel('Log_loss')\n", "    plt.title('Evolution des erreurs')\n", "    return W,b"]}, {"cell_type": "code", "execution_count": 119, "id": "6f992b57", "metadata": {}, "outputs": [], "source": ["# W,b=regression_logistique(X, y)"]}, {"cell_type": "code", "execution_count": 120, "id": "4573ad54-4767-4a48-83d4-a497b35429f9", "metadata": {}, "outputs": [], "source": ["W,b=regression_logistique(X_train, y_train)"]}, {"cell_type": "markdown", "id": "80c77dbf", "metadata": {}, "source": ["### 5. 1 Evaluation du modèle et Visualisation\n"]}, {"cell_type": "code", "execution_count": 121, "id": "cbbc3886", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[[5.48301840e-01]\n", " [9.11102811e-01]\n", " [8.90117220e-01]\n", " [9.93375232e-01]\n", " [9.99364735e-01]\n", " [2.19128848e-01]\n", " [9.42303597e-01]\n", " [3.05931868e-01]\n", " [9.87894625e-01]\n", " [9.95546320e-01]\n", " [9.51357937e-01]\n", " [7.23651595e-01]\n", " [9.93545040e-01]\n", " [4.40367469e-01]\n", " [1.58167630e-03]\n", " [2.17477294e-03]\n", " [9.99903280e-01]\n", " [9.02660423e-01]\n", " [5.55666813e-01]\n", " [9.54665765e-01]\n", " [6.58407785e-01]\n", " [9.97727540e-01]\n", " [9.97180328e-01]\n", " [9.52553300e-01]\n", " [3.89013177e-01]\n", " [9.97008036e-01]\n", " [9.96501808e-01]\n", " [9.42303597e-01]\n", " [3.14609743e-01]\n", " [9.98485407e-01]\n", " [9.90543745e-01]\n", " [1.62467073e-02]\n", " [9.99792511e-01]\n", " [8.38813514e-01]\n", " [9.79750727e-01]\n", " [1.39835135e-02]\n", " [9.71589820e-01]\n", " [8.53357800e-01]\n", " [9.97827463e-01]\n", " [8.92389444e-01]\n", " [9.89314222e-01]\n", " [9.93053409e-01]\n", " [6.97337951e-01]\n", " [9.26952274e-01]\n", " [9.58230356e-01]\n", " [3.47318409e-02]\n", " [3.89910035e-04]\n", " [9.77410519e-01]\n", " [9.95150129e-01]\n", " [2.83141614e-02]\n", " [9.58122457e-01]\n", " [1.91399228e-03]\n", " [2.73262794e-03]\n", " [9.97016057e-01]\n", " [9.97817653e-01]\n", " [9.90543745e-01]\n", " [3.40204382e-01]\n", " [9.81463696e-01]\n", " [9.94371818e-01]\n", " [9.53720716e-01]\n", " [7.75749751e-02]\n", " [6.14903810e-02]\n", " [4.77501666e-01]\n", " [9.37353890e-03]\n", " [2.13024901e-01]\n", " [2.76978986e-03]\n", " [5.06905063e-02]\n", " [9.89753027e-01]\n", " [9.93727327e-01]\n", " [9.75890226e-01]\n", " [9.03057772e-02]\n", " [8.77445570e-01]\n", " [1.10544343e-03]\n", " [9.68336936e-01]\n", " [9.91962835e-01]\n", " [2.17904630e-02]\n", " [3.13922553e-02]\n", " [2.38581248e-03]\n", " [9.99777800e-01]\n", " [1.77367547e-02]\n", " [4.66281124e-03]\n", " [4.45252633e-01]\n", " [4.05625307e-01]\n", " [6.78380969e-01]\n", " [9.31800796e-01]\n", " [8.78700573e-01]\n", " [9.91351747e-01]\n", " [9.97380655e-01]\n", " [9.41910494e-01]\n", " [8.96222187e-01]\n", " [8.86542597e-01]\n", " [4.45702844e-01]\n", " [9.92650481e-01]\n", " [1.01555636e-04]\n", " [8.02575191e-01]\n", " [8.55710845e-01]\n", " [7.13076470e-01]\n", " [8.91695348e-01]\n", " [7.69906583e-01]\n", " [9.92670098e-01]\n", " [4.67001955e-05]\n", " [9.96764776e-01]\n", " [3.02311435e-01]\n", " [9.93392928e-01]\n", " [9.95939531e-01]\n", " [9.93040825e-01]\n", " [9.95609779e-01]\n", " [7.94660605e-02]\n", " [9.87894625e-01]\n", " [8.73017619e-01]\n", " [1.56391580e-02]\n", " [9.36349597e-01]\n", " [9.90226246e-01]\n", " [8.93079625e-01]\n", " [2.52378624e-01]\n", " [1.27597084e-02]\n", " [9.99545612e-01]\n", " [2.31362437e-02]\n", " [4.32787487e-02]\n", " [9.83391141e-01]\n", " [4.07876861e-04]\n", " [7.46798917e-01]\n", " [9.98770836e-01]\n", " [9.25348803e-01]\n", " [3.69063863e-04]\n", " [6.57391608e-01]\n", " [3.90495214e-02]\n", " [5.91794406e-01]\n", " [9.99202160e-01]\n", " [9.99568744e-01]\n", " [9.98120346e-01]\n", " [2.80182406e-01]\n", " [2.04096541e-01]\n", " [7.62644237e-01]\n", " [9.38152196e-01]\n", " [9.84223716e-01]\n", " [5.64898115e-02]\n", " [9.90433309e-01]\n", " [8.86813137e-01]\n", " [1.02045069e-01]\n", " [8.81458986e-01]\n", " [9.95997410e-01]\n", " [9.91998751e-01]\n", " [9.58410694e-01]\n", " [5.63883675e-01]\n", " [3.72858238e-01]\n", " [9.98261857e-01]\n", " [9.75890226e-01]\n", " [9.80122891e-01]\n", " [9.99392712e-01]\n", " [9.92972075e-01]\n", " [9.48880993e-01]\n", " [9.50474414e-01]\n", " [9.12263441e-01]\n", " [5.13085784e-01]\n", " [9.83273010e-01]\n", " [9.70448789e-01]\n", " [4.67394731e-01]\n", " [5.48465478e-03]\n", " [9.86386175e-01]\n", " [9.98481329e-01]\n", " [9.93871788e-01]\n", " [6.49842507e-01]\n", " [3.11900798e-01]\n", " [4.49717782e-01]\n", " [7.58214743e-01]\n", " [9.98438336e-01]\n", " [9.97292081e-01]\n", " [7.21075031e-05]\n", " [6.95349118e-02]\n", " [2.62016492e-01]\n", " [5.34018617e-02]\n", " [9.99258950e-01]\n", " [9.97392424e-01]\n", " [8.70292669e-02]\n", " [9.35757016e-01]\n", " [4.59103758e-01]\n", " [9.69128586e-01]\n", " [9.88827130e-01]\n", " [8.58360075e-01]\n", " [2.68527621e-05]\n", " [1.62555296e-04]\n", " [9.45314671e-01]\n", " [1.89529517e-02]\n", " [6.73938087e-03]\n", " [6.70065544e-01]\n", " [1.00810592e-01]\n", " [6.14561420e-03]\n", " [7.55722934e-01]\n", " [1.32824025e-03]\n", " [3.87940586e-01]\n", " [9.52020678e-01]\n", " [9.64231282e-01]\n", " [4.39704033e-01]\n", " [4.22244088e-01]\n", " [9.98190053e-01]\n", " [9.91692583e-01]\n", " [8.27928163e-01]\n", " [3.85798667e-01]\n", " [3.04196854e-02]\n", " [6.51480767e-01]\n", " [7.99496668e-03]\n", " [8.86153855e-02]\n", " [1.59755350e-02]\n", " [9.88065804e-01]\n", " [8.56374450e-01]\n", " [9.96796545e-01]\n", " [6.06516553e-01]\n", " [1.00402257e-02]\n", " [9.93231409e-01]\n", " [4.97762615e-01]\n", " [6.48504479e-02]\n", " [7.49514928e-01]\n", " [2.19440860e-01]\n", " [2.83883321e-02]\n", " [9.57781664e-02]\n", " [9.73186451e-01]\n", " [9.19014530e-01]\n", " [1.65146055e-03]\n", " [2.66049030e-01]\n", " [9.27134377e-01]\n", " [8.20352809e-01]\n", " [9.83317236e-01]\n", " [6.27910052e-03]\n", " [9.64231282e-01]\n", " [9.93297640e-01]\n", " [9.47954941e-01]\n", " [8.66798117e-01]\n", " [2.82401789e-02]\n", " [2.07953506e-02]\n", " [9.36188940e-01]\n", " [9.92112358e-01]\n", " [9.58981486e-01]\n", " [9.89633453e-01]\n", " [3.77619757e-03]\n", " [9.95128289e-01]\n", " [1.08307605e-02]\n", " [6.46971762e-01]\n", " [3.58672538e-02]\n", " [3.78356611e-01]\n", " [7.69429264e-01]\n", " [7.45944226e-01]\n", " [9.82638794e-01]\n", " [9.83812692e-01]\n", " [2.28525992e-01]\n", " [3.98357413e-02]\n", " [6.20618719e-01]\n", " [9.83435060e-01]\n", " [8.92389444e-01]\n", " [9.99517777e-01]\n", " [5.85419022e-02]\n", " [2.91697870e-02]\n", " [1.48245310e-01]\n", " [9.82187226e-01]\n", " [8.71108997e-01]\n", " [1.84730222e-02]\n", " [8.67108680e-01]\n", " [8.86542597e-01]\n", " [5.79552377e-01]\n", " [9.69128586e-01]\n", " [6.44906596e-01]\n", " [9.99192760e-01]\n", " [9.99422574e-01]\n", " [7.92980490e-01]\n", " [9.96244608e-01]\n", " [9.27922346e-01]\n", " [9.35048701e-01]\n", " [2.06211689e-04]\n", " [8.53357800e-01]\n", " [9.88150494e-01]\n", " [3.53343889e-03]\n", " [5.38702011e-03]\n", " [1.20195205e-03]\n", " [1.19781791e-02]\n", " [8.65270358e-02]\n", " [6.10174849e-03]\n", " [8.26251722e-01]\n", " [9.91148410e-01]\n", " [9.86578394e-01]\n", " [8.65270358e-02]\n", " [3.75559349e-02]\n", " [3.29424478e-03]\n", " [5.73170544e-01]\n", " [9.27922346e-01]\n", " [9.80436775e-01]\n", " [6.58407785e-01]\n", " [6.01020591e-02]\n", " [9.44186083e-01]\n", " [2.88408907e-03]\n", " [9.75440798e-01]\n", " [9.99073517e-01]\n", " [4.47584912e-02]\n", " [4.58096602e-02]\n", " [9.93844228e-01]\n", " [9.86129620e-01]\n", " [4.70898354e-03]\n", " [9.75890226e-01]\n", " [7.81047466e-01]\n", " [8.21412550e-01]\n", " [9.73490652e-01]\n", " [6.17859222e-03]\n", " [4.39472811e-03]\n", " [9.52020678e-01]\n", " [4.73716523e-02]\n", " [7.25090616e-01]\n", " [9.82060688e-01]\n", " [9.87980515e-01]\n", " [9.82958801e-01]\n", " [9.97542635e-01]\n", " [9.96892948e-01]\n", " [4.34660809e-02]\n", " [3.57400664e-01]\n", " [9.99964846e-01]\n", " [8.44814209e-01]\n", " [9.77250836e-01]\n", " [9.85434830e-01]\n", " [7.51664276e-03]\n", " [8.64281757e-01]\n", " [5.71178607e-02]\n", " [9.97967338e-01]\n", " [8.27928163e-01]\n", " [9.95986661e-01]\n", " [2.99504012e-03]\n", " [3.06574037e-05]\n", " [3.45562503e-04]\n", " [4.71683178e-02]\n", " [9.38413650e-01]\n", " [6.37856382e-01]\n", " [7.55369571e-02]\n", " [6.27808702e-01]\n", " [1.43413995e-04]\n", " [8.84162940e-01]\n", " [4.99181754e-03]\n", " [9.66227082e-01]\n", " [9.76620877e-01]\n", " [5.34940650e-02]\n", " [1.87227517e-01]\n", " [9.98463450e-01]\n", " [4.83170045e-02]\n", " [9.72807723e-01]\n", " [1.48107225e-03]\n", " [9.43067968e-02]\n", " [1.59738741e-03]\n", " [9.28702450e-01]\n", " [7.49172609e-01]\n", " [4.03556701e-03]\n", " [9.87489303e-01]\n", " [1.22665310e-01]\n", " [9.43805041e-01]\n", " [4.82898730e-01]\n", " [9.23841359e-01]\n", " [8.03278097e-02]\n", " [1.01220604e-01]\n", " [7.33982358e-01]\n", " [9.77509992e-01]\n", " [4.15881069e-01]\n", " [9.97868115e-01]\n", " [9.70986852e-01]\n", " [9.93345453e-01]\n", " [2.65830795e-02]\n", " [9.93665315e-01]\n", " [3.74094943e-04]\n", " [2.47980690e-01]\n", " [2.14996724e-01]\n", " [6.88711227e-01]\n", " [9.79170784e-01]\n", " [9.83998320e-01]\n", " [9.99159380e-01]\n", " [9.87160565e-03]\n", " [9.72495906e-01]\n", " [1.11641254e-02]\n", " [2.21392750e-02]\n", " [8.14705436e-01]\n", " [7.96070837e-01]\n", " [1.68402591e-01]\n", " [4.25551868e-01]\n", " [4.53287591e-01]\n", " [2.13343028e-02]\n", " [9.55170406e-01]\n", " [6.08235317e-01]\n", " [1.14931774e-01]\n", " [4.66270952e-01]\n", " [9.93871788e-01]\n", " [4.04640327e-03]\n", " [5.84415356e-02]\n", " [6.13351836e-02]\n", " [6.79365256e-01]\n", " [9.88391041e-01]\n", " [1.44084099e-01]\n", " [2.91400927e-01]\n", " [2.90469522e-01]\n", " [1.62432993e-01]\n", " [7.32572714e-01]\n", " [9.97913621e-01]\n", " [4.32050729e-03]\n", " [7.96507590e-01]\n", " [9.91883521e-01]\n", " [9.94002086e-01]\n", " [8.50056715e-01]\n", " [8.77641419e-01]\n", " [8.94873807e-01]\n", " [3.73235400e-03]\n", " [5.81962789e-01]\n", " [4.33936465e-01]\n", " [9.94999922e-01]\n", " [6.37234203e-01]\n", " [1.97161637e-01]\n", " [9.87633300e-01]\n", " [7.48159351e-01]\n", " [3.49240400e-03]\n", " [9.99468502e-01]\n", " [6.98275989e-02]\n", " [9.90407764e-01]\n", " [9.38828880e-01]\n", " [3.99388529e-02]\n", " [9.90543745e-01]\n", " [9.96254668e-01]\n", " [9.87894625e-01]\n", " [2.35754016e-01]\n", " [9.99451967e-01]\n", " [9.95891849e-01]\n", " [1.89367828e-02]\n", " [9.99756192e-01]\n", " [1.43642686e-03]\n", " [1.18796114e-03]\n", " [9.92754902e-01]\n", " [8.97555252e-01]\n", " [9.83273010e-01]\n", " [3.20083514e-01]\n", " [2.80430581e-02]\n", " [6.95244422e-01]\n", " [9.88666746e-01]\n", " [9.59440127e-01]\n", " [1.69414359e-01]\n", " [9.60718037e-01]\n", " [4.41480458e-01]\n", " [5.45994879e-02]\n", " [9.07157830e-01]\n", " [6.12308397e-04]\n", " [5.97450514e-02]\n", " [9.89034345e-01]\n", " [1.23443078e-01]\n", " [7.86244219e-01]\n", " [7.74571460e-04]\n", " [9.39857337e-03]\n", " [9.08739912e-01]\n", " [7.14548836e-01]\n", " [1.56525563e-02]\n", " [9.58662572e-01]\n", " [9.96117768e-01]\n", " [1.25506029e-01]\n", " [7.95774806e-01]\n", " [9.39651499e-01]\n", " [7.11599573e-01]\n", " [5.07751180e-03]\n", " [9.97272547e-01]\n", " [9.96244608e-01]\n", " [3.53064953e-01]\n", " [9.96162838e-01]\n", " [9.94264879e-01]\n", " [4.49892906e-02]\n", " [9.91451673e-01]\n", " [3.42713938e-03]\n", " [8.87534575e-01]\n", " [9.91021045e-01]\n", " [9.27438805e-01]\n", " [2.90421989e-02]\n", " [6.68470188e-01]\n", " [9.99722163e-01]\n", " [1.75850718e-01]\n", " [5.38023829e-01]\n", " [4.49717782e-01]\n", " [9.89131847e-01]\n", " [1.40020490e-01]\n", " [1.80850252e-02]\n", " [1.44125046e-02]\n", " [8.79180985e-01]\n", " [9.92369059e-01]\n", " [8.70091727e-01]\n", " [9.90826585e-01]\n", " [8.85451506e-02]\n", " [4.38296314e-03]\n", " [9.90269846e-01]\n", " [5.28601177e-01]\n", " [4.83600266e-04]\n", " [9.88876902e-01]\n", " [5.66764255e-01]\n", " [8.72216465e-01]\n", " [3.87301471e-01]\n", " [9.52877984e-01]\n", " [9.99309846e-01]\n", " [1.05566874e-01]\n", " [9.97952665e-01]\n", " [9.94770632e-01]\n", " [2.56207663e-02]\n", " [9.98207851e-01]\n", " [9.76662453e-01]\n", " [6.31170710e-01]\n", " [9.35048701e-01]\n", " [9.99153304e-01]\n", " [3.10542276e-01]\n", " [4.17189790e-01]\n", " [1.03887515e-03]\n", " [4.46153144e-01]\n", " [9.72807723e-01]\n", " [9.59158720e-01]\n", " [9.97560240e-01]\n", " [8.53694410e-01]\n", " [6.55158407e-01]\n", " [9.93297640e-01]\n", " [9.96711990e-01]\n", " [9.68775934e-01]\n", " [1.22469309e-01]\n", " [6.89678360e-01]\n", " [9.95910279e-01]\n", " [9.80349987e-01]\n", " [8.08149946e-03]\n", " [8.51884789e-01]\n", " [9.82638794e-01]\n", " [4.10590769e-02]\n", " [2.12781587e-02]\n", " [5.53483576e-02]\n", " [7.72812570e-03]\n", " [9.89679670e-01]\n", " [4.47833616e-03]\n", " [9.90041327e-01]\n", " [8.53693910e-04]\n", " [5.69193483e-01]\n", " [9.94223634e-01]\n", " [1.58206484e-02]\n", " [7.91683110e-03]\n", " [8.45166873e-01]\n", " [9.99902318e-01]\n", " [9.92348643e-01]\n", " [7.74880004e-03]\n", " [8.94181935e-02]\n", " [1.93772942e-02]\n", " [9.98956596e-01]\n", " [9.17113937e-01]\n", " [8.69274878e-01]\n", " [9.78231496e-01]\n", " [1.24901836e-02]\n", " [9.10884494e-01]\n", " [6.58407785e-01]\n", " [9.64231282e-01]\n", " [3.78634002e-03]\n", " [3.11323236e-01]\n", " [9.03057772e-02]\n", " [9.99960512e-01]\n", " [6.28573603e-02]\n", " [9.65753516e-01]\n", " [9.96100269e-01]\n", " [9.59719677e-01]\n", " [9.55170406e-01]\n", " [7.49514928e-01]\n", " [9.56464776e-01]\n", " [7.60460064e-03]\n", " [1.54264188e-01]\n", " [8.26251722e-01]\n", " [8.69787088e-01]\n", " [9.84445987e-01]\n", " [9.97843031e-01]\n", " [4.75703750e-01]\n", " [7.86244219e-01]\n", " [1.33531658e-01]\n", " [9.98373810e-01]\n", " [9.97448058e-01]\n", " [2.00899067e-01]\n", " [4.09545966e-01]\n", " [3.66225227e-02]\n", " [9.55478018e-01]\n", " [9.91021045e-01]\n", " [9.77667898e-01]\n", " [9.92940496e-01]\n", " [7.48159351e-01]\n", " [9.77980467e-01]\n", " [9.58590288e-01]\n", " [9.95308434e-01]\n", " [8.47627882e-01]\n", " [1.31872632e-01]\n", " [6.64261689e-01]\n", " [1.09805358e-01]\n", " [1.72516078e-04]\n", " [9.96679386e-01]\n", " [8.56598468e-01]\n", " [9.79371742e-01]\n", " [9.89437438e-01]\n", " [1.42091706e-02]\n", " [8.91955098e-01]\n", " [4.46331239e-04]\n", " [4.10491309e-03]\n", " [9.57171589e-01]\n", " [2.34642839e-02]\n", " [9.61698997e-02]\n", " [8.39785623e-01]\n", " [9.96145541e-01]\n", " [9.84692670e-01]\n", " [9.98797097e-01]\n", " [2.40075378e-02]\n", " [9.94044904e-01]\n", " [6.21703627e-02]\n", " [1.07709575e-01]\n", " [4.15231185e-02]\n", " [5.57445622e-01]\n", " [2.58888914e-01]\n", " [9.77726608e-01]\n", " [9.91250664e-01]\n", " [2.52185168e-02]\n", " [1.35123388e-05]\n", " [3.90137927e-02]\n", " [8.41486536e-04]\n", " [7.32411578e-02]\n", " [8.79944484e-01]\n", " [7.64759673e-01]\n", " [1.51464228e-01]\n", " [9.15596348e-01]\n", " [1.33320932e-01]\n", " [1.12031531e-01]\n", " [4.02161320e-02]\n", " [5.08355519e-01]\n", " [2.74578261e-01]\n", " [7.33740568e-03]\n", " [9.95578163e-01]\n", " [1.25793948e-02]\n", " [8.50399575e-01]\n", " [2.19837761e-02]\n", " [9.99633963e-01]\n", " [9.96460705e-01]\n", " [8.99282800e-01]\n", " [3.49779457e-01]\n", " [9.75890226e-01]\n", " [6.17218858e-01]\n", " [9.81199621e-01]\n", " [9.01625563e-01]\n", " [9.97711140e-01]\n", " [5.21127342e-03]\n", " [9.88717224e-01]\n", " [9.71911612e-01]\n", " [9.97342725e-01]\n", " [1.66514222e-01]\n", " [6.81517898e-01]\n", " [7.67402938e-02]\n", " [6.07158939e-01]\n", " [1.96238545e-04]\n", " [9.95287304e-01]\n", " [9.99337256e-01]\n", " [9.57061078e-01]\n", " [9.41763000e-01]\n", " [1.49963775e-01]\n", " [8.40677665e-02]\n", " [8.66275957e-01]\n", " [9.98708436e-01]\n", " [1.54854164e-01]\n", " [1.71797449e-02]\n", " [9.83435060e-01]\n", " [9.48530256e-01]\n", " [6.83080235e-01]\n", " [9.95093223e-01]\n", " [9.99057546e-01]\n", " [2.11671417e-04]\n", " [3.55890704e-03]\n", " [8.86087664e-01]\n", " [8.50399575e-01]\n", " [7.49009098e-01]\n", " [4.62685427e-01]\n", " [7.15468860e-01]\n", " [9.95880818e-01]\n", " [7.40442011e-01]\n", " [2.59156134e-03]\n", " [4.31700123e-04]\n", " [9.61858626e-01]\n", " [4.59638606e-03]\n", " [9.91535170e-01]\n", " [7.81047466e-01]\n", " [9.98971514e-01]\n", " [2.59853029e-03]\n", " [9.52020678e-01]\n", " [9.97996370e-01]\n", " [9.99744956e-01]\n", " [9.98241398e-01]\n", " [9.93755408e-01]\n", " [9.23012476e-01]\n", " [9.02262994e-01]\n", " [1.74779202e-03]\n", " [3.74118286e-01]\n", " [9.92735510e-01]\n", " [9.86732735e-01]\n", " [9.97349851e-01]\n", " [9.90475995e-01]\n", " [5.07751180e-03]\n", " [9.95832450e-01]\n", " [1.56748038e-03]\n", " [8.27283991e-01]\n", " [9.53200556e-01]\n", " [9.84335239e-01]\n", " [8.26637913e-01]\n", " [9.97749876e-01]\n", " [9.63318803e-01]\n", " [9.74806808e-01]\n", " [1.25126828e-02]\n", " [9.71589820e-01]\n", " [9.98318764e-01]\n", " [8.44221358e-01]\n", " [2.56480213e-01]\n", " [9.65753516e-01]\n", " [9.58981486e-01]\n", " [6.00261705e-01]\n", " [1.64773889e-01]\n", " [2.60274178e-01]\n", " [9.93451932e-01]\n", " [2.10762979e-01]\n", " [1.32455283e-02]\n", " [6.94286946e-01]\n", " [7.04163941e-02]\n", " [9.97807799e-01]\n", " [5.14430999e-01]\n", " [8.83422715e-01]\n", " [9.99912014e-01]\n", " [4.40816666e-01]\n", " [1.97571297e-02]\n", " [2.86343616e-03]\n", " [9.97858488e-01]\n", " [2.92979186e-02]\n", " [1.39696591e-01]\n", " [8.85358143e-01]\n", " [1.16129603e-01]\n", " [9.45453691e-01]\n", " [7.14867048e-02]\n", " [9.83581522e-01]\n", " [9.99961807e-01]\n", " [9.98866483e-01]\n", " [9.07763081e-01]\n", " [1.88326773e-01]\n", " [1.23734712e-01]\n", " [5.35108900e-01]\n", " [8.23517343e-01]\n", " [5.04736810e-02]\n", " [9.99101459e-01]\n", " [9.95022334e-01]\n", " [9.37310503e-01]\n", " [9.82958801e-01]\n", " [9.18878783e-01]\n", " [9.97448058e-01]\n", " [2.80430581e-02]\n", " [9.37044680e-01]\n", " [9.77090050e-01]\n", " [8.27928163e-01]\n", " [9.97889072e-01]\n", " [9.68557176e-01]\n", " [1.10691136e-01]\n", " [9.78536350e-01]\n", " [9.95832450e-01]\n", " [9.99217109e-01]\n", " [9.81804961e-01]\n", " [9.67069689e-01]\n", " [9.97605880e-01]\n", " [9.02660423e-01]\n", " [2.46485524e-02]\n", " [9.83927074e-01]\n", " [9.86920126e-01]\n", " [1.32181177e-01]\n", " [6.71656995e-01]\n", " [2.19589909e-01]\n", " [6.20618719e-01]\n", " [9.65842454e-01]\n", " [9.97566784e-01]\n", " [1.84141484e-03]\n", " [8.38813514e-01]\n", " [9.77726608e-01]\n", " [7.93115826e-03]\n", " [7.42854778e-01]\n", " [9.84514969e-01]\n", " [9.96004669e-01]\n", " [2.97770448e-01]\n", " [7.99126737e-01]\n", " [9.92972075e-01]\n", " [8.35144507e-02]\n", " [9.97495792e-01]\n", " [4.76829952e-01]\n", " [9.75093097e-01]\n", " [9.94770632e-01]\n", " [9.91751751e-01]\n", " [6.88413245e-02]\n", " [1.27826869e-02]\n", " [9.95461596e-01]\n", " [9.62644003e-01]\n", " [7.36787393e-01]\n", " [9.99963752e-01]\n", " [7.18025364e-01]\n", " [9.11320645e-01]\n", " [9.86920126e-01]\n", " [9.98555941e-01]\n", " [8.81458986e-01]\n", " [9.99207885e-01]\n", " [9.79697241e-01]\n", " [9.99049005e-01]\n", " [9.45686066e-01]\n", " [2.92330472e-03]\n", " [7.29379653e-01]\n", " [1.03958596e-02]\n", " [2.79484403e-03]\n", " [5.84157980e-01]\n", " [9.85187238e-01]\n", " [2.37343944e-02]\n", " [8.28311402e-01]\n", " [4.12383641e-01]\n", " [4.24749979e-04]\n", " [9.98744003e-01]\n", " [9.19748301e-01]\n", " [3.05765181e-03]\n", " [9.74156885e-01]\n", " [5.93973989e-01]\n", " [9.31224717e-01]\n", " [3.94227128e-02]\n", " [1.48706153e-01]\n", " [9.23523082e-01]\n", " [9.70032584e-01]\n", " [9.99084304e-01]\n", " [9.62082295e-05]\n", " [3.02474793e-03]\n", " [7.18394213e-01]\n", " [4.67394731e-01]\n", " [4.56194181e-01]\n", " [7.69013117e-04]\n", " [9.47239152e-01]\n", " [9.52877984e-01]\n", " [8.30965388e-04]\n", " [9.28402915e-01]\n", " [9.86885324e-01]\n", " [9.98670709e-01]\n", " [9.40463758e-01]\n", " [4.19062930e-03]\n", " [9.49919683e-01]\n", " [9.96711990e-01]\n", " [7.26524975e-01]\n", " [2.76236288e-03]\n", " [9.91632994e-01]\n", " [7.31687256e-01]\n", " [9.76435737e-01]\n", " [3.35968216e-01]\n", " [2.16893798e-03]\n", " [9.07763081e-01]\n", " [9.94223634e-01]\n", " [9.29178214e-01]\n", " [1.32455283e-02]\n", " [9.77410519e-01]\n", " [9.98846947e-01]\n", " [1.20317305e-02]\n", " [2.72610750e-01]\n", " [8.83145139e-01]\n", " [9.78135143e-01]\n", " [9.65693188e-01]\n", " [8.44221358e-01]\n", " [9.94182094e-01]\n", " [9.48530256e-01]\n", " [2.88933496e-03]\n", " [7.64759673e-01]\n", " [9.99521238e-01]\n", " [3.01743844e-01]\n", " [2.28320840e-02]\n", " [9.64479033e-01]\n", " [6.35919668e-04]\n", " [3.86120098e-02]\n", " [7.04708379e-01]\n", " [9.98449533e-01]\n", " [1.40670174e-01]\n", " [4.76837133e-03]\n", " [3.37985719e-01]\n", " [9.31341348e-01]\n", " [3.27086554e-04]\n", " [7.47651705e-01]\n", " [9.98928994e-01]\n", " [3.39703103e-04]\n", " [9.99596555e-01]\n", " [9.87309962e-01]\n", " [9.99641790e-01]\n", " [7.71416245e-03]\n", " [2.70823841e-01]\n", " [9.34773955e-01]\n", " [1.27441418e-03]\n", " [9.82638794e-01]\n", " [7.66053836e-01]\n", " [1.09018291e-01]\n", " [1.08214442e-02]\n", " [1.03586251e-04]\n", " [9.47013049e-01]\n", " [9.47373552e-01]\n", " [7.05357856e-02]\n", " [9.22820930e-01]\n", " [1.68147514e-01]\n", " [9.98042708e-01]\n", " [9.99585876e-01]\n", " [9.99369294e-01]\n", " [3.04789638e-01]\n", " [6.07158939e-01]\n", " [9.82515408e-01]\n", " [9.94831261e-01]\n", " [9.02843656e-03]\n", " [9.34167736e-01]\n", " [9.67525667e-01]\n", " [9.95086893e-03]\n", " [9.46650217e-01]\n", " [9.07537395e-01]\n", " [8.94193835e-01]\n", " [7.34241245e-02]\n", " [2.82160368e-04]\n", " [9.27922346e-01]\n", " [6.34942573e-01]\n", " [8.78190901e-02]\n", " [3.76662888e-01]\n", " [9.99533584e-01]\n", " [1.15634347e-03]\n", " [2.76919356e-01]\n", " [6.87163936e-01]\n", " [9.14121895e-01]\n", " [3.37187323e-02]\n", " [1.10931114e-04]\n", " [6.27500882e-02]\n", " [6.35566436e-01]\n", " [8.33750617e-02]\n", " [1.54264188e-01]\n", " [9.89160753e-01]\n", " [9.51357937e-01]\n", " [9.92787304e-01]\n", " [8.10727232e-01]\n", " [2.16297095e-04]\n", " [5.17852189e-03]\n", " [8.94957836e-02]\n", " [9.96616143e-01]\n", " [9.67383507e-01]\n", " [1.77050304e-02]\n", " [8.35618405e-01]\n", " [9.91474462e-01]\n", " [6.89678360e-01]\n", " [9.90226246e-01]\n", " [9.27922346e-01]\n", " [1.90405998e-01]\n", " [1.04234208e-05]\n", " [3.21968168e-02]\n", " [9.87980515e-01]\n", " [2.16672280e-01]\n", " [9.69047929e-01]\n", " [1.93426935e-02]\n", " [9.55452393e-02]\n", " [5.96961998e-02]\n", " [9.77667898e-01]\n", " [9.64878455e-01]\n", " [9.97721427e-01]\n", " [5.14639802e-03]\n", " [9.99370989e-01]\n", " [4.34660809e-02]\n", " [9.23332701e-01]\n", " [9.85830740e-01]\n", " [4.07762329e-02]\n", " [9.17594416e-01]\n", " [8.94800324e-03]\n", " [9.85931065e-01]\n", " [9.12624144e-01]\n", " [9.91312952e-01]\n", " [9.25162598e-01]\n", " [8.11830730e-01]\n", " [2.93449341e-01]\n", " [6.37118595e-02]\n", " [9.30827548e-02]\n", " [5.90415753e-02]\n", " [9.95558242e-01]\n", " [9.90956684e-01]\n", " [1.23734712e-01]\n", " [9.99153304e-01]\n", " [9.77410519e-01]\n", " [9.52348825e-01]\n", " [1.36833843e-03]\n", " [3.91802988e-01]\n", " [9.98816448e-01]\n", " [1.54597169e-02]\n", " [8.47627882e-01]\n", " [8.14298635e-01]\n", " [2.15419874e-02]\n", " [9.88234591e-01]\n", " [7.53890727e-01]\n", " [2.04356123e-05]\n", " [9.48661545e-01]\n", " [9.97220569e-01]\n", " [6.15083194e-01]\n", " [9.60203322e-03]\n", " [7.31175473e-02]\n", " [7.37318848e-02]\n", " [8.51843960e-02]\n", " [9.35757016e-01]\n", " [9.35048701e-01]\n", " [5.99319487e-03]\n", " [2.39181736e-01]\n", " [7.85484431e-01]\n", " [2.78123348e-04]\n", " [9.97167604e-01]\n", " [9.05255777e-03]\n", " [8.65270358e-02]\n", " [7.39573345e-01]\n", " [8.05978818e-01]\n", " [9.11467822e-01]]\n", "Accuracy= 0.908\n"]}], "source": ["from sklearn.metrics import accuracy_score\n", "y_pred = predict(X_test, W, b)\n", "print(\"Accuracy=\",accuracy_score(y_test, y_pred))"]}, {"cell_type": "code", "execution_count": 122, "id": "fc2e330e", "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAjMAAAHFCAYAAAAHcXhbAAAAOnRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjEwLjAsIGh0dHBzOi8vbWF0cGxvdGxpYi5vcmcvlHJYcgAAAAlwSFlzAAAPYQAAD2EBqD+naQAAPOxJREFUeJzt3Xl81NW9//H3l4QMSUhGAmaDECKKiCwqKLJUQFmMiCB6rzugvVariIgWFfrTFIWgVqUWwa0NeBVRy1IrSgkiUS6grMpigZagCAkgSxIQgknO7w/MNwwJSGaSnAl5PR+PedQ5c2bmkxNr3o/zPed7HGOMEQAAQC1Vz3YBAAAAgSDMAACAWo0wAwAAajXCDAAAqNUIMwAAoFYjzAAAgFqNMAMAAGo1wgwAAKjVCDMAAKBWI8wA1WzatGlyHOekj8WLF1fbd7do0ULDhg3z670zZszQpEmTKnzNcRylpaX5XVdVS0tLk+M4tssAYEmo7QKAuiIjI0OtW7cu196mTRsL1fyyGTNmaP369Ro5cmS515YtW6ZmzZrVfFEAUAHCDFBD2rZtq06dOtkuo0pcfvnltkuo1X788UdFRERU+Nrhw4cVHh5uvQ6gNuEyExAkLr74Yv3qV78q115cXKymTZtq8ODBbtu+fft03333qWnTpgoLC9M555yjsWPHqrCw8JTfUXrJa9u2bT7tixcv9rnk1bNnT82bN0/ffvutzyWxUhVdZlq/fr0GDhyoRo0aqUGDBrrooos0ffr0Cr/nnXfe0dixY5WYmKjo6Gj17t1bmzZtOo1RkubNm6eLLrpIHo9HKSkp+uMf/1hhP2OMpkyZoosuukjh4eFq1KiRbrzxRm3dutWn35o1a3TttdcqNjZWHo9HiYmJ6t+/v77//vtfrGXhwoW66qqrFB0drYiICHXr1k2ffPKJT5/SS2CrV6/WjTfeqEaNGqlly5aSjl0GvPbaazV79mxdfPHFatCggf7whz9IknJzc3XPPfeoWbNmCgsLU0pKiv7whz+oqKio3HieeKly27ZtchxH06ZNc9uGDRumhg0bat26derbt6+ioqJ01VVXBTwGQDBgZgaoIcXFxT5/iKRjoSAkJESSdOedd+rBBx/Uli1bdN5557l9FixYoJ07d+rOO++UJB05ckS9evXSf/7zH/3hD39Q+/bt9fnnnys9PV1r167VvHnzAq51ypQp+s1vfqP//Oc/mjNnzi/237Rpk7p27arY2Fi99NJLaty4sd566y0NGzZMu3bt0ujRo336jxkzRt26ddMbb7yh/Px8PfrooxowYIC++eYbdzwq8sknn2jgwIHq0qWLZs6cqeLiYj377LPatWtXub733HOPpk2bphEjRuiZZ57Rvn37NG7cOHXt2lVfffWV4uLidOjQIfXp00cpKSl6+eWXFRcXp9zcXH366acqKCg45c/81ltvaciQIRo4cKCmT5+u+vXr69VXX1W/fv30z3/+0w0KpQYPHqybb75Z9957rw4dOuS2r169Wt98841+//vfKyUlRZGRkcrNzdVll12mevXq6YknnlDLli21bNkyPf3009q2bZsyMjJ+8XdSkaNHj+q6667TPffco8cee0xFRUUBjQEQNAyAapWRkWEkVfgICQlx+/3www8mLCzMjBkzxuf9//3f/23i4uLMTz/9ZIwx5pVXXjGSzHvvvefT75lnnjGSzIIFC9y25ORkM3To0HK1ZGdn+7z3008/NZLMp59+6rb179/fJCcnV/gzSTJPPvmk+/zmm282Ho/HfPfddz79UlNTTUREhDlw4IDP91xzzTU+/d577z0jySxbtqzC7yvVuXNnk5iYaA4fPuy25efnm5iYGHP8f86WLVtmJJnnn3/e5/3bt2834eHhZvTo0cYYY1auXGkkmblz557ye0906NAhExMTYwYMGODTXlxcbDp06GAuu+wyt+3JJ580kswTTzxR7nOSk5NNSEiI2bRpk0/7PffcYxo2bGi+/fZbn/Y//vGPRpLZsGGDMabi35sxxmRnZxtJJiMjw20bOnSokWT++te/+vT1dwyAYMJlJqCGvPnmm1qxYoXP44svvnBfb9y4sQYMGKDp06erpKREkrR//379/e9/15AhQxQaemwiddGiRYqMjNSNN97o8/mlu5ZOvMxRExYtWqSrrrpKSUlJ5Wr68ccftWzZMp/26667zud5+/btJUnffvvtSb/j0KFDWrFihQYPHqwGDRq47VFRURowYIBP3w8//FCO4+j2229XUVGR+4iPj1eHDh3cyzLnnnuuGjVqpEcffVSvvPKKNm7ceFo/79KlS7Vv3z4NHTrU5/NLSkp09dVXa8WKFT6zL5J0ww03VPhZ7du3V6tWrcrV36tXLyUmJvp8fmpqqiQpKyvrtOqsyIl1+DsGQDDhMhNQQy644IJfXAB81113adasWcrMzFS/fv30zjvvqLCw0Gd79d69exUfH19uK3JsbKxCQ0O1d+/e6ij/lPbu3auEhIRy7YmJie7rx2vcuLHPc4/HI+nY4teT2b9/v0pKShQfH1/utRPbdu3aJWOM4uLiKvysc845R5Lk9XqVlZWl8ePHa8yYMdq/f78SEhJ099136/e//73q169f4ftLL2udGCiPt2/fPkVGRrrPKxqfk7Xv2rVL//jHP076/T/88MNJv/dUIiIiFB0d7dPm7xgAwYQwAwSRfv36KTExURkZGerXr58yMjLUuXNnn+3bjRs31hdffCFjjE+g2b17t4qKitSkSZOTfn7pjMaJC4X9/eN4fE05OTnl2nfu3ClJp6zpdDVq1EiO4yg3N7fcaye2NWnSRI7j6PPPP3eD0vGOb2vXrp1mzpwpY4y+/vprTZs2TePGjVN4eLgee+yxCmsp/Xn+/Oc/n3Rn14lB6mT3wamovUmTJmrfvr3Gjx9f4XtKQ2Jlf58nq8GfMQCCCZeZgCASEhKiO+64Q3PnztXnn3+ulStX6q677vLpc9VVV+ngwYOaO3euT/ubb77pvn4yLVq0kCR9/fXXPu0ffPBBub4ej+eUMyUn1rRo0SI3vBxfU0RERJVs5Y6MjNRll12m2bNn68iRI257QUGB/vGPf/j0vfbaa2WM0Y4dO9SpU6dyj3bt2pX7fMdx1KFDB7344os666yztHr16pPW0q1bN5111lnauHFjhZ/fqVMnhYWF+f2zXnvttVq/fr1atmxZ4WeXhpnK/D5PR2XGAAgmzMwANWT9+vXldjNJUsuWLXX22We7z++66y4988wzuvXWWxUeHq6bbrrJp/+QIUP08ssva+jQodq2bZvatWunJUuWaMKECbrmmmvUu3fvk9Zw6aWX6vzzz9cjjzyioqIiNWrUSHPmzNGSJUvK9W3Xrp1mz56tqVOnqmPHjqpXr95JL5M9+eST7jqPJ554QjExMXr77bc1b948Pfvss/J6vac7TKf01FNP6eqrr1afPn308MMPq7i4WM8884wiIyO1b98+t1+3bt30m9/8RnfeeadWrlypK664QpGRkcrJydGSJUvUrl07/fa3v9WHH36oKVOmaNCgQTrnnHNkjNHs2bN14MAB9enT56R1NGzYUH/+8581dOhQ7du3TzfeeKNiY2O1Z88effXVV9qzZ4+mTp3q9885btw4ZWZmqmvXrhoxYoTOP/98HTlyRNu2bdNHH32kV155Rc2aNVN8fLx69+6t9PR0NWrUSMnJyfrkk080e/bs0/4uf8cACCoWFx8DdcKpdjNJMq+//nq593Tt2tVIMrfddluFn7l3715z7733moSEBBMaGmqSk5PN448/bo4cOeLT78TdTMYYs3nzZtO3b18THR1tzj77bPPAAw+YefPmldsVs2/fPnPjjTeas846yziO47NbSCfsZjLGmHXr1pkBAwYYr9drwsLCTIcOHXx20xhTtvvm/fff92mvaPfNyXzwwQemffv2JiwszDRv3txMnDjR3TF0or/+9a+mc+fOJjIy0oSHh5uWLVuaIUOGmJUrVxpjjPnXv/5lbrnlFtOyZUsTHh5uvF6vueyyy8y0adN+sQ5jjMnKyjL9+/c3MTExpn79+qZp06amf//+Pj9faW179uwp9/7k5GTTv3//Cj97z549ZsSIESYlJcXUr1/fxMTEmI4dO5qxY8eagwcPuv1ycnLMjTfeaGJiYozX6zW33367u0PpxN1MkZGR5b4n0DEAgoFjjDE1H6EAAACqBmtmAABArUaYAQAAtRphBgAA1GqEGQAAUKsRZgAAQK1GmAEAALXaGX/TvJKSEu3cuVNRUVEnvZU3AAAILsYYFRQUKDExUfXqnXru5YwPMzt37ix3ki8AAKgdtm/frmbNmp2yzxkfZqKioiQdG4wTT4sFAADBKT8/X0lJSe7f8VM548NM6aWl6OhowgwAALXM6SwRsboAeOrUqWrfvr0bNLp06aKPP/7YfX3YsGFyHMfnURWn7wIAgDOH1ZmZZs2aaeLEiTr33HMlSdOnT9fAgQO1Zs0aXXjhhZKkq6++WhkZGe57wsLCrNQKAACCk9UwM2DAAJ/n48eP19SpU7V8+XI3zHg8HsXHx9soDwAA1AJBc5+Z4uJizZw5U4cOHVKXLl3c9sWLFys2NlatWrXS3Xffrd27d5/ycwoLC5Wfn+/zAAAAZy7rYWbdunVq2LChPB6P7r33Xs2ZM0dt2rSRJKWmpurtt9/WokWL9Pzzz2vFihW68sorVVhYeNLPS09Pl9frdR9sywYA4MzmGGOMzQKOHj2q7777TgcOHNCsWbP0xhtvKCsryw00x8vJyVFycrJmzpypwYMHV/h5hYWFPmGndGtXXl4eu5kAAKgl8vPz5fV6T+vvt/Wt2WFhYe4C4E6dOmnFihX605/+pFdffbVc34SEBCUnJ2vLli0n/TyPxyOPx1Nt9QIAgOBi/TLTiYwxJ72MtHfvXm3fvl0JCQk1XBUAAAhWVmdmxowZo9TUVCUlJamgoEAzZ87U4sWLNX/+fB08eFBpaWm64YYblJCQoG3btmnMmDFq0qSJrr/+eptlAwCAIGI1zOzatUt33HGHcnJy5PV61b59e82fP199+vTR4cOHtW7dOr355ps6cOCAEhIS1KtXL7377rundWtjAABQN1hfAFzdKrOACAAABIfK/P0OujUzAAAAlWF9N1NttaegUJtyC+QNr692zby2ywEAoM5iZsZPX2bv0+1/+UJPzdtouxQAAOo0woyf3BPJz+gVRwAABD/CjJ/KsgxpBgAAmwgzfiqdmTmz94IBABD8CDN+O5ZmyDIAANhFmPFT2cwMcQYAAJsIM35i/S8AAMGBMOMn5+epGSZmAACwizDjJ2ZmAAAIDoQZP5XdZ4Y4AwCATYQZP7kLgO2WAQBAnUeY8ZMj1swAABAMCDP+cmdmSDMAANhEmPETS2YAAAgOhBk/sTUbAIDgQJjxE1uzAQAIDoQZP3GcAQAAwYEw4yfHnZsBAAA2EWb8VDYzY7cOAADqOsKMn8rWzJBmAACwiTDjL2ZmAAAICoQZP7l3ALZcBwAAdR1hxk/sZgIAIDgQZvzEfWYAAAgOhBk/ORybDQBAUCDM+IksAwBAcCDM+KnsoEniDAAANhFm/MTMDAAAwYEw4zdOzQYAIBgQZvxUNjNDmgEAwCbCjJ/K1sxYLQMAgDqPMOOn0q3ZhBkAAOwizPjJ+eUuAACgBhBm/MRxBgAABAfCjJ84aBIAgOBAmPFT6cxMCTMzAABYRZjxU9llJrt1AABQ11kNM1OnTlX79u0VHR2t6OhodenSRR9//LH7ujFGaWlpSkxMVHh4uHr27KkNGzZYrLgMl5kAAAgOVsNMs2bNNHHiRK1cuVIrV67UlVdeqYEDB7qB5dlnn9ULL7ygyZMna8WKFYqPj1efPn1UUFBgs2xJzMwAABAsrIaZAQMG6JprrlGrVq3UqlUrjR8/Xg0bNtTy5ctljNGkSZM0duxYDR48WG3bttX06dP1448/asaMGTbLllQWZpibAQDArqBZM1NcXKyZM2fq0KFD6tKli7Kzs5Wbm6u+ffu6fTwej3r06KGlS5ee9HMKCwuVn5/v86gODmczAQAQFKyHmXXr1qlhw4byeDy69957NWfOHLVp00a5ubmSpLi4OJ/+cXFx7msVSU9Pl9frdR9JSUnVUjenZgMAEBysh5nzzz9fa9eu1fLly/Xb3/5WQ4cO1caNG93XHcf3XrvGmHJtx3v88ceVl5fnPrZv314tdZedzUScAQDAplDbBYSFhencc8+VJHXq1EkrVqzQn/70Jz366KOSpNzcXCUkJLj9d+/eXW625ngej0cej6d6ixYzMwAABAvrMzMnMsaosLBQKSkpio+PV2Zmpvva0aNHlZWVpa5du1qssBRrZgAACAZWZ2bGjBmj1NRUJSUlqaCgQDNnztTixYs1f/58OY6jkSNHasKECTrvvPN03nnnacKECYqIiNCtt95qs2xJnM0EAECwsBpmdu3apTvuuEM5OTnyer1q37695s+frz59+kiSRo8ercOHD+u+++7T/v371blzZy1YsEBRUVE2y5Z03JoZq1UAAADHnOFTC/n5+fJ6vcrLy1N0dHSVfW72D4fU64+LFeUJ1bo/9KuyzwUAAJX7+x10a2ZqC2ZmAAAIDoQZP7FmBgCA4ECY8RMHTQIAEBwIM37ioEkAAIIDYSZAhrkZAACsIsz4iZkZAACCA2HGT6XnQ5FlAACwizDjJ/eoS9IMAABWEWb8VHbQJGkGAACbCDN+cjhoEgCAoECY8VPZzAwAALCJMOMn9zgDpmYAALCKMOMvZmYAAAgKhBk/sWYGAIDgQJjxk+P8ch8AAFD9CDN+Oj7LsG4GAAB7CDN+co6bmiHLAABgD2HGTz4zM9aqAAAAhBk/Hb9mhstMAADYQ5jxk3Pc3AxRBgAAewgz/vKZmbFXBgAAdR1hxk8+l5mYmwEAwBrCjJ98t2ZbKwMAgDqPMOMnh7vmAQAQFAgzfmJmBgCA4ECY8RNrZgAACA6EGT/5bM0mywAAYA1hxk++MzMAAMAWwkwV4A7AAADYQ5jxEzMzAAAEB8KMn1gzAwBAcCDM+Mnh2GwAAIICYcZPvlmGNAMAgC2EGT8dfwdgLjMBAGAPYcZP9Y6bmikhzQAAYA1hxk8+MzMW6wAAoK4jzFQBJmYAALCHMBOA0skZFgADAGAPYSYA7oUmsgwAANZYDTPp6em69NJLFRUVpdjYWA0aNEibNm3y6TNs2DA5juPzuPzyyy1V7Kt03QxZBgAAe6yGmaysLN1///1avny5MjMzVVRUpL59++rQoUM+/a6++mrl5OS4j48++shSxb5KZ2ZYMwMAgD2hNr98/vz5Ps8zMjIUGxurVatW6YorrnDbPR6P4uPja7q8X8SaGQAA7AuqNTN5eXmSpJiYGJ/2xYsXKzY2Vq1atdLdd9+t3bt3n/QzCgsLlZ+f7/OoLqXnMzEzAwCAPUETZowxGjVqlLp37662bdu67ampqXr77be1aNEiPf/881qxYoWuvPJKFRYWVvg56enp8nq97iMpKan6inZnZgAAgC2OMcExr3D//fdr3rx5WrJkiZo1a3bSfjk5OUpOTtbMmTM1ePDgcq8XFhb6BJ38/HwlJSUpLy9P0dHRVVrz+b//WIVFJVryaC81axRRpZ8NAEBdlp+fL6/Xe1p/v62umSn1wAMP6IMPPtBnn312yiAjSQkJCUpOTtaWLVsqfN3j8cjj8VRHmeW4a2aCIg4CAFA3WQ0zxhg98MADmjNnjhYvXqyUlJRffM/evXu1fft2JSQk1ECFp+b4nJ0NAABssLpm5v7779dbb72lGTNmKCoqSrm5ucrNzdXhw4clSQcPHtQjjzyiZcuWadu2bVq8eLEGDBigJk2a6Prrr7dZuiRmZgAACAZWZ2amTp0qSerZs6dPe0ZGhoYNG6aQkBCtW7dOb775pg4cOKCEhAT16tVL7777rqKioixU7Mu9zwxLgAEAsMb6ZaZTCQ8P1z//+c8aqqby3DsAk2UAALAmaLZm10ZlMzMAAMAWwkwg3DUzxBkAAGwhzASAmRkAAOwjzASANTMAANhHmAmA495mhjQDAIAthJkAuJeZyDIAAFhDmAmAe5nJch0AANRlhJkAMDMDAIB9hJkAuMcZMDcDAIA1hJmAsJsJAADbCDMB4KBJAADsI8wEgIMmAQCwjzATAGZmAACwjzATAMedmwEAALYQZgLAzAwAAPYRZgLAmhkAAOwjzASAgyYBALCPMFMFyDIAANhDmAlA2ZoZ4gwAALYQZgJQdpwBAACwhTATAIfjDAAAsI4wEwDHvc0MaQYAAFsIMwFwt2aTZQAAsIYwEwB3a7blOgAAqMsIMwFgZgYAAPsIM4FgazYAANYRZgJQdpwBAACwhTATAI4zAADAPsJMADhoEgAA+wgzAXC4zgQAgHWEmQC4dwC2XAcAAHUZYSYAZQdN2q0DAIC6jDATgNIFwCWkGQAArCHMBIAlMwAA2EeYCYDDTfMAALCOMBMAN8zYLQMAgDqNMBMAR6QZAABsI8wEoGxmhjQDAIAtVsNMenq6Lr30UkVFRSk2NlaDBg3Spk2bfPoYY5SWlqbExESFh4erZ8+e2rBhg6WKfXFqNgAA9lkNM1lZWbr//vu1fPlyZWZmqqioSH379tWhQ4fcPs8++6xeeOEFTZ48WStWrFB8fLz69OmjgoICi5X/jLOZAACwLtTml8+fP9/neUZGhmJjY7Vq1SpdccUVMsZo0qRJGjt2rAYPHixJmj59uuLi4jRjxgzdc889Nsp2sTUbAAD7gmrNTF5eniQpJiZGkpSdna3c3Fz17dvX7ePxeNSjRw8tXbq0ws8oLCxUfn6+z6O6sDUbAAD7gibMGGM0atQode/eXW3btpUk5ebmSpLi4uJ8+sbFxbmvnSg9PV1er9d9JCUlVVvNzMwAAGBf0ISZ4cOH6+uvv9Y777xT7jXHPZ76GGNMubZSjz/+uPLy8tzH9u3bq6Xe4+tiYgYAAHusrpkp9cADD+iDDz7QZ599pmbNmrnt8fHxko7N0CQkJLjtu3fvLjdbU8rj8cjj8VRvwT8ri1OkGQAAbPF7Zmb+/PlasmSJ+/zll1/WRRddpFtvvVX79+8/rc8wxmj48OGaPXu2Fi1apJSUFJ/XU1JSFB8fr8zMTLft6NGjysrKUteuXf0tvcpwajYAAPb5HWZ+97vfuYtr161bp4cffljXXHONtm7dqlGjRp3WZ9x///166623NGPGDEVFRSk3N1e5ubk6fPiwpGOXcUaOHKkJEyZozpw5Wr9+vYYNG6aIiAjdeuut/pZeZUrvAEyWAQDAHr8vM2VnZ6tNmzaSpFmzZunaa6/VhAkTtHr1al1zzTWn9RlTp06VJPXs2dOnPSMjQ8OGDZMkjR49WocPH9Z9992n/fv3q3PnzlqwYIGioqL8Lb3qMDMDAIB1foeZsLAw/fjjj5KkhQsXasiQIZKObas+3e3Qp7Ol2XEcpaWlKS0tzd9Sq03ZbibSDAAAtvgdZrp3765Ro0apW7du+vLLL/Xuu+9KkjZv3uyziPdMxpoZAADs83vNzOTJkxUaGqq//e1vmjp1qpo2bSpJ+vjjj3X11VdXWYHBjDUzAADY5/fMTPPmzfXhhx+Wa3/xxRcDKqg24Q7AAADY5/fMzOrVq7Vu3Tr3+d///ncNGjRIY8aM0dGjR6ukuGB3kvv2AQCAGuR3mLnnnnu0efNmSdLWrVt18803KyIiQu+//75Gjx5dZQUGM/cyExMzAABY43eY2bx5sy666CJJ0vvvv68rrrhCM2bM0LRp0zRr1qyqqi+ouZeZWDUDAIA1focZY4xKSkokHduaXXpvmaSkJP3www9VU10twcwMAAD2+B1mOnXqpKefflr/+7//q6ysLPXv31/SsZvpnezcpDMNB00CAGCf32Fm0qRJWr16tYYPH66xY8fq3HPPlST97W9/C4pzk2pC2U3zAACALX5vzW7fvr3PbqZSzz33nEJCQgIqqrZgazYAAPb5HWZKrVq1St98840cx9EFF1ygSy65pCrqqhWYmQEAwD6/w8zu3bt10003KSsrS2eddZaMMcrLy1OvXr00c+ZMnX322VVZZ1ByyrYzAQAAS/xeM/PAAw+ooKBAGzZs0L59+7R//36tX79e+fn5GjFiRFXWGLQ4aBIAAPv8npmZP3++Fi5cqAsuuMBta9OmjV5++WX17du3SooLdhw0CQCAfX7PzJSUlKh+/frl2uvXr+/ef+bMx0GTAADY5neYufLKK/Xggw9q586dbtuOHTv00EMP6aqrrqqS4oIdMzMAANjnd5iZPHmyCgoK1KJFC7Vs2VLnnnuuUlJSVFBQoD//+c9VWWPQYs0MAAD2+b1mJikpSatXr1ZmZqb+9a9/yRijNm3aqHfv3lVZX1BjZgYAAPsCvs9Mnz591KdPn6qopdZxWDMDAIB1lQozL7300mn3rQvbsx33OhNxBgAAWyoVZl588cXT6uc4Tp0KM0QZAADsqVSYyc7Orq46aiX3MhNpBgAAa/zezXS6oqOjtXXr1ur+Gjs4aBIAAOuqPcycyX/oOWgSAAD7qj3MnMlKD5o8g/MaAABBjzATAGZmAACwjzATAIc1MwAAWFftYcZxb8Zy5jlzfzIAAGoPFgAHgDUzAADYV+1h5uOPP1bTpk2r+2us4KBJAADs8/tsplGjRlXY7jiOGjRooHPPPVcDBw5U9+7d/S4u2JXOzJSQZQAAsMbvMLNmzRqtXr1axcXFOv/882WM0ZYtWxQSEqLWrVtrypQpevjhh7VkyRK1adOmKmsOGpyaDQCAfX5fZho4cKB69+6tnTt3atWqVVq9erV27NihPn366JZbbtGOHTt0xRVX6KGHHqrKeoMKl5kAALDP7zDz3HPP6amnnlJ0dLTbFh0drbS0ND377LOKiIjQE088oVWrVlVJocGImRkAAOzzO8zk5eVp9+7d5dr37Nmj/Px8SdJZZ52lo0eP+l9dkHPYnA0AgHUBXWa66667NGfOHH3//ffasWOH5syZo1//+tcaNGiQJOnLL79Uq1atqqrWoMNN8wAAsM/vBcCvvvqqHnroId18880qKio69mGhoRo6dKhefPFFSVLr1q31xhtvVE2lQYjLTAAA2Od3mGnYsKFef/11vfjii9q6dauMMWrZsqUaNmzo9rnooouqosYg9vNN8yxXAQBAXeZ3mCnVsGFDxcTEyHEcnyBTFzAzAwCAfX6vmSkpKdG4cePk9XqVnJys5s2b66yzztJTTz2lkpKS0/qMzz77TAMGDFBiYqIcx9HcuXN9Xh82bJgcx/F5XH755f6WXOXYmg0AgH1+z8yMHTtWf/nLXzRx4kR169ZNxhj93//9n9LS0nTkyBGNHz/+Fz/j0KFD6tChg+68807dcMMNFfa5+uqrlZGR4T4PCwvzt+Qqx8wMAAD2+R1mpk+frjfeeEPXXXed29ahQwc1bdpU991332mFmdTUVKWmpp6yj8fjUXx8vL9lViuHNTMAAFjn92Wmffv2qXXr1uXaW7durX379gVU1PEWL16s2NhYtWrVSnfffXeF97Y5XmFhofLz830e1cVxrzMRZwAAsMXvMNOhQwdNnjy5XPvkyZPVvn37gIoqlZqaqrfffluLFi3S888/rxUrVujKK69UYWHhSd+Tnp4ur9frPpKSkqqkloqUrZkBAAC2+H2Z6dlnn1X//v21cOFCdenSRY7jaOnSpdq+fbs++uijKinupptucv+5bdu26tSpk5KTkzVv3jwNHjy4wvc8/vjjPid65+fnV1ugKT01m4kZAADs8XtmpkePHtq8ebOuv/56HThwQPv27dPgwYO1YcMGnwW7VSkhIUHJycnasmXLSft4PB5FR0f7PKobu5kAALAnoPvMJCYmllvo+9VXX2n69On661//GlBhFdm7d6+2b9+uhISEKv9sf7CbCQAA+wK+aV4gDh48qH//+9/u8+zsbK1du1YxMTGKiYlRWlqabrjhBiUkJGjbtm0aM2aMmjRpouuvv95i1WXYzQQAgH1Ww8zKlSvVq1cv93npWpehQ4dq6tSpWrdund58800dOHBACQkJ6tWrl959911FRUXZKtkHMzMAANhnNcz07NnzlCdO//Of/6zBaiqPOwADAGBfpcPMyXYRlTpw4IC/tdQ6DnuzAQCwrtJhxuv1/uLrQ4YM8bug2sTdmm25DgAA6rJKh5nq2nZdG5XdAJg4AwCALX7fZwZy0wxZBgAAewgzAWBrNgAA9hFmAsDWbAAA7CPMBICt2QAA2EeYCQAzMwAA2EeYCYDjzs0AAABbCDMBKJuZYWoGAABbCDMB4AbAAADYR5gJROkdgEkzAABYQ5gJALuZAACwjzATAHYzAQBgH2EmANwBGAAA+wgzAWBmBgAA+wgzASi7ywxpBgAAWwgzAWBmBgAA+wgzAXDYmg0AgHWEmSrA1mwAAOwhzASAy0wAANhHmAkAW7MBALCPMBMAZmYAALCPMBMAjjMAAMA+wkwAHI7NBgDAOsJMAFgzAwCAfYSZAJStmSHOAABgC2GmCpSQZQAAsIYwE4B6DpeZAACwjTATAC4zAQBgH2EmAGxmAgDAPsJMABx3asZuHQAA1GWEmQCUZRnSDAAAthBmAuBeZiLLAABgDWEmEKW7mQgzAABYQ5gJAGczAQBgH2EmAJyaDQCAfYSZAHA2EwAA9hFmAsDMDAAA9lkNM5999pkGDBigxMREOY6juXPn+rxujFFaWpoSExMVHh6unj17asOGDXaKrYDj/hNpBgAAW6yGmUOHDqlDhw6aPHlyha8/++yzeuGFFzR58mStWLFC8fHx6tOnjwoKCmq40ooxMwMAgH2hNr88NTVVqampFb5mjNGkSZM0duxYDR48WJI0ffp0xcXFacaMGbrnnntqstQKsWYGAAD7gnbNTHZ2tnJzc9W3b1+3zePxqEePHlq6dOlJ31dYWKj8/HyfR7XhoEkAAKwL2jCTm5srSYqLi/Npj4uLc1+rSHp6urxer/tISkqqtho5aBIAAPuCNsyUcg9z/Jkxplzb8R5//HHl5eW5j+3bt1d7bUzMAABgj9U1M6cSHx8v6dgMTUJCgtu+e/fucrM1x/N4PPJ4PNVen8TMDAAAwSBoZ2ZSUlIUHx+vzMxMt+3o0aPKyspS165dLVZWxmHNDAAA1lmdmTl48KD+/e9/u8+zs7O1du1axcTEqHnz5ho5cqQmTJig8847T+edd54mTJigiIgI3XrrrRarLnOKq10AAKCGWA0zK1euVK9evdzno0aNkiQNHTpU06ZN0+jRo3X48GHdd9992r9/vzp37qwFCxYoKirKVsk+3K3ZTMwAAGCN1TDTs2fPU16icRxHaWlpSktLq7miKsG9zMSqGQAArAnaNTO1CTMzAADYQ5gJAFuzAQCwjzATgLKt2aQZAABsIcwEgIMmAQCwjzATAA6aBADAPsJMABxuAQwAgHWEmQCwZgYAAPsIMwFgzQwAAPYRZgLCmhkAAGwjzASAgyYBALCPMBMA1v8CAGAfYSYA3AEYAAD7CDMBYGYGAAD7CDMBKLvPDHEGAABbCDMBcBcA2y0DAIA6jTATAPc4A9IMAADWEGYC4c7MkGYAALCFMBMAlswAAGAfYSYAbM0GAMA+wkwA2JoNAIB9hJkAcJwBAAD2EWYC4LhzMwAAwBbCTADKZmbs1gEAQF1GmAlA2ZoZ0gwAALYQZgLxc5opIcsAAGANYSYA9dyt2aQZAABsIcwEgK3ZAADYR5gJgMNJkwAAWEeYCQBZBgAA+wgzASg7m4k4AwCALYSZADAzAwCAfYSZgHDQJAAAthFmAlA2M0OaAQDAFsJMAMrWzFgtAwCAOo0wEwDH4TITAAC2EWYCwJnZAADYR5gJQNmp2UzNAABgC2EmAE7pbibLdQAAUJcFfZhJS0uT4zg+j/j4eNtlSTp+ZsZuHQAA1GWhtgs4HRdeeKEWLlzoPg8JCbFYTXlszQYAwJ5aEWZCQ0ODZjbmeMzMAABgX9BfZpKkLVu2KDExUSkpKbr55pu1detW2yVJYs0MAADBIOhnZjp37qw333xTrVq10q5du/T000+ra9eu2rBhgxo3blyuf2FhoQoLC93n+fn51VYbMzMAANgX9DMzqampuuGGG9SuXTv17t1b8+bNkyRNnz69wv7p6enyer3uIykpqdpqc9wbzZBmAACwJejDzIkiIyPVrl07bdmypcLXH3/8ceXl5bmP7du3V1stDgdNAgBgXdBfZjpRYWGhvvnmG/3qV7+q8HWPxyOPx1MjtZQdNAkAAGwJ+pmZRx55RFlZWcrOztYXX3yhG2+8Ufn5+Ro6dKjt0o47aJI4AwCALUE/M/P999/rlltu0Q8//KCzzz5bl19+uZYvX67k5GTbpTEzAwBAEAj6MDNz5kzbJZwCa2YAALAt6C8zBTMOmgQAwD7CTADcNTNWqwAAoG4jzATAYdEMAADWEWYCwMwMAAD2EWYCwJoZAADsI8wEgIMmAQCwjzATAA6aBADAPsJMFTDMzQAAYA1hJgDMzAAAYB9hJgClW7PJMgAA2EOYCUBovWNhpriEOAMAgC2EmQA0qB8i6ViY+am4xHI1AADUTYSZAIT/HGYk6cejxRYrAQCg7iLMBKB+iKOQny81HfmJMAMAgA2EmQA4juPOzhxmZgYAACsIMwEqXTdzmJkZAACsIMwEKDzs2BASZgAAsIMwE6DIsFBJ0sEjRZYrAQCgbiLMBCg6vL4kKf/IT5YrAQCgbiLMBMj7c5jJO0yYAQDABsJMgKIb/Dwzc5jLTAAA2ECYCRAzMwAA2EWYCVB0+LEFwIQZAADsIMwEyMsCYAAArCLMBKhszQxhBgAAGwgzAXJnZggzAABYQZgJkDeCBcAAANhEmAlQ6WUmwgwAAHYQZgJUtgC4SMYYy9UAAFD3EGYCVLo1u7jE6NBRDpsEAKCmEWYCFF4/RGGhx4Zx/6GjlqsBAKDuIcwEyHEcxUZ5JEm7C45YrgYAgLqHMFMF4qMbSJJy8wotVwIAQN1DmKkCcd5jYWZXPjMzAADUNMJMFYiLIswAAGALYaYKJMWES5L+s+eQ5UoAAKh7CDNVoG1TryRp/Y487jUDAEANI8xUgTYJ0QoLrafc/CNavyPfdjkAANQphJkqEOkJVb8L4yVJj83+Wv/efdByRQAA1B2htgs4HVOmTNFzzz2nnJwcXXjhhZo0aZJ+9atf2S7Lx+h+5+uzzXu0YWe+er+QpaZnheucsyMVH91AZ0XUV3hYqCLCQhQRFqLQevUUUk+q5zgKqXfsUfrPpf/rHPfZjnOSfy7t5dN2fF/nJO0VfMYJ7QAAnK7GDcPUOj7a2vcHfZh59913NXLkSE2ZMkXdunXTq6++qtTUVG3cuFHNmze3XZ4rKSZCc+/vpvHzvtGif+3SjgOHtePAYdtlAQBQ7fq3S9DLt11i7fsdE+QrVjt37qxLLrlEU6dOddsuuOACDRo0SOnp6b/4/vz8fHm9XuXl5Sk6umZS48HCIq37Pk/f7/9Ru/KPqOBIkX48Wvzzo0hFJUYlJUbFxqi4xKik9H9L5LaV8vnlmPLtx//2zHG9fdp9+hzfHtS/egBALfGr85pobP82VfqZlfn7HdQzM0ePHtWqVav02GOP+bT37dtXS5curfA9hYWFKiwsuxNvfn7NL8ht6AlVl5aNJTWu8e8GAKCuCeoFwD/88IOKi4sVFxfn0x4XF6fc3NwK35Oeni6v1+s+kpKSaqJUAABgSVCHmVLOCStTjTHl2ko9/vjjysvLcx/bt2+viRIBAIAlQX2ZqUmTJgoJCSk3C7N79+5yszWlPB6PPB5PTZQHAACCQFDPzISFhaljx47KzMz0ac/MzFTXrl0tVQUAAIJJUM/MSNKoUaN0xx13qFOnTurSpYtee+01fffdd7r33nttlwYAAIJA0IeZm266SXv37tW4ceOUk5Ojtm3b6qOPPlJycrLt0gAAQBAI+vvMBMrGfWYAAEBgKvP3O6jXzAAAAPwSwgwAAKjVCDMAAKBWI8wAAIBajTADAABqNcIMAACo1QgzAACgVgv6m+YFqvQ2Ovn5+ZYrAQAAp6v07/bp3A7vjA8zBQUFkqSkpCTLlQAAgMoqKCiQ1+s9ZZ8z/g7AJSUl2rlzp6KiouQ4TpV+dn5+vpKSkrR9+3buLlyNGOeawTjXDMa5ZjDONae6xtoYo4KCAiUmJqpevVOvijnjZ2bq1aunZs2aVet3REdH83+WGsA41wzGuWYwzjWDca451THWvzQjU4oFwAAAoFYjzAAAgFqNMBMAj8ejJ598Uh6Px3YpZzTGuWYwzjWDca4ZjHPNCYaxPuMXAAMAgDMbMzMAAKBWI8wAAIBajTADAABqNcIMAACo1QgzfpoyZYpSUlLUoEEDdezYUZ9//rntkoJWenq6Lr30UkVFRSk2NlaDBg3Spk2bfPoYY5SWlqbExESFh4erZ8+e2rBhg0+fwsJCPfDAA2rSpIkiIyN13XXX6fvvv/fps3//ft1xxx3yer3yer264447dODAger+EYNSenq6HMfRyJEj3TbGuers2LFDt99+uxo3bqyIiAhddNFFWrVqlfs6Yx24oqIi/f73v1dKSorCw8N1zjnnaNy4cSopKXH7MM6V99lnn2nAgAFKTEyU4ziaO3euz+s1OabfffedBgwYoMjISDVp0kQjRozQ0aNHK/9DGVTazJkzTf369c3rr79uNm7caB588EETGRlpvv32W9ulBaV+/fqZjIwMs379erN27VrTv39/07x5c3Pw4EG3z8SJE01UVJSZNWuWWbdunbnppptMQkKCyc/Pd/vce++9pmnTpiYzM9OsXr3a9OrVy3To0MEUFRW5fa6++mrTtm1bs3TpUrN06VLTtm1bc+2119bozxsMvvzyS9OiRQvTvn178+CDD7rtjHPV2Ldvn0lOTjbDhg0zX3zxhcnOzjYLFy40//73v90+jHXgnn76adO4cWPz4YcfmuzsbPP++++bhg0bmkmTJrl9GOfK++ijj8zYsWPNrFmzjCQzZ84cn9drakyLiopM27ZtTa9evczq1atNZmamSUxMNMOHD6/0z0SY8cNll11m7r33Xp+21q1bm8cee8xSRbXL7t27jSSTlZVljDGmpKTExMfHm4kTJ7p9jhw5Yrxer3nllVeMMcYcOHDA1K9f38ycOdPts2PHDlOvXj0zf/58Y4wxGzduNJLM8uXL3T7Lli0zksy//vWvmvjRgkJBQYE577zzTGZmpunRo4cbZhjnqvPoo4+a7t27n/R1xrpq9O/f39x1110+bYMHDza33367MYZxrgonhpmaHNOPPvrI1KtXz+zYscPt88477xiPx2Py8vIq9XNwmamSjh49qlWrVqlv374+7X379tXSpUstVVW75OXlSZJiYmIkSdnZ2crNzfUZU4/Hox49erhjumrVKv30008+fRITE9W2bVu3z7Jly+T1etW5c2e3z+WXXy6v11unfjf333+/+vfvr969e/u0M85V54MPPlCnTp30X//1X4qNjdXFF1+s119/3X2dsa4a3bt31yeffKLNmzdLkr766istWbJE11xzjSTGuTrU5JguW7ZMbdu2VWJiotunX79+Kiws9LlkezrO+IMmq9oPP/yg4uJixcXF+bTHxcUpNzfXUlW1hzFGo0aNUvfu3dW2bVtJcsetojH99ttv3T5hYWFq1KhRuT6l78/NzVVsbGy574yNja0zv5uZM2dq9erVWrFiRbnXGOeqs3XrVk2dOlWjRo3SmDFj9OWXX2rEiBHyeDwaMmQIY11FHn30UeXl5al169YKCQlRcXGxxo8fr1tuuUUS/05Xh5oc09zc3HLf06hRI4WFhVV63AkzfnIcx+e5MaZcG8obPny4vv76ay1ZsqTca/6M6Yl9KupfV34327dv14MPPqgFCxaoQYMGJ+3HOAeupKREnTp10oQJEyRJF198sTZs2KCpU6dqyJAhbj/GOjDvvvuu3nrrLc2YMUMXXnih1q5dq5EjRyoxMVFDhw51+zHOVa+mxrSqxp3LTJXUpEkThYSElEuNu3fvLpcw4euBBx7QBx98oE8//VTNmjVz2+Pj4yXplGMaHx+vo0ePav/+/afss2vXrnLfu2fPnjrxu1m1apV2796tjh07KjQ0VKGhocrKytJLL72k0NBQdwwY58AlJCSoTZs2Pm0XXHCBvvvuO0n8O11Vfve73+mxxx7TzTffrHbt2umOO+7QQw89pPT0dEmMc3WoyTGNj48v9z379+/XTz/9VOlxJ8xUUlhYmDp27KjMzEyf9szMTHXt2tVSVcHNGKPhw4dr9uzZWrRokVJSUnxeT0lJUXx8vM+YHj16VFlZWe6YduzYUfXr1/fpk5OTo/Xr17t9unTpory8PH355Zduny+++EJ5eXl14ndz1VVXad26dVq7dq376NSpk2677TatXbtW55xzDuNcRbp161bu9gKbN29WcnKyJP6drio//vij6tXz/TMVEhLibs1mnKteTY5ply5dtH79euXk5Lh9FixYII/Ho44dO1au8EotF4Yxpmxr9l/+8hezceNGM3LkSBMZGWm2bdtmu7Sg9Nvf/tZ4vV6zePFik5OT4z5+/PFHt8/EiRON1+s1s2fPNuvWrTO33HJLhVsBmzVrZhYuXGhWr15trrzyygq3ArZv394sW7bMLFu2zLRr1+6M3V55Oo7fzWQM41xVvvzySxMaGmrGjx9vtmzZYt5++20TERFh3nrrLbcPYx24oUOHmqZNm7pbs2fPnm2aNGliRo8e7fZhnCuvoKDArFmzxqxZs8ZIMi+88IJZs2aNe3uRmhrT0q3ZV111lVm9erVZuHChadasGVuza9LLL79skpOTTVhYmLnkkkvcbcYoT1KFj4yMDLdPSUmJefLJJ018fLzxeDzmiiuuMOvWrfP5nMOHD5vhw4ebmJgYEx4ebq699lrz3Xff+fTZu3evue2220xUVJSJiooyt912m9m/f38N/JTB6cQwwzhXnX/84x+mbdu2xuPxmNatW5vXXnvN53XGOnD5+fnmwQcfNM2bNzcNGjQw55xzjhk7dqwpLCx0+zDOlffpp59W+N/koUOHGmNqdky//fZb079/fxMeHm5iYmLM8OHDzZEjRyr9MznGGFO5uRwAAIDgwZoZAABQqxFmAABArUaYAQAAtRphBgAA1GqEGQAAUKsRZgAAQK1GmAEAALUaYQZAjXEcR3PnzrVdho/FixfLcRwdOHDAdikA/ESYAVBjcnJylJqaKknatm2bHMfR2rVra+z7e/bsqZEjR/q0de3aVTk5OfJ6vTVWB4CqFWq7AAB1R+mJvFXtp59+Uv369f16b1hYWLXVBaBmMDMDoFJ69uypESNGaPTo0YqJiVF8fLzS0tJO673HX2YqPT394osvluM46tmzp9svIyNDF1xwgRo0aKDWrVtrypQp7mulMzrvvfeeevbsqQYNGuitt97S3r17dcstt6hZs2aKiIhQu3bt9M4777jvGzZsmLKysvSnP/1JjuPIcRxt27atwstMs2bN0oUXXiiPx6MWLVro+eef9/k5WrRooQkTJuiuu+5SVFSUmjdvrtdee61yAwmg6lT6NCcAdVqPHj1MdHS0SUtLM5s3bzbTp083juOYBQsW/OJ7JZk5c+YYY46dPC3JLFy40OTk5Ji9e/caY4x57bXXTEJCgpk1a5bZunWrmTVrlomJiTHTpk0zxhiTnZ1tJJkWLVq4fXbs2GG+//5789xzz5k1a9aY//znP+all14yISEhZvny5cYYYw4cOGC6dOli7r77bvfk9qKiIvfQvdID8FauXGnq1atnxo0bZzZt2mQyMjJMeHi4z8GoycnJJiYmxrz88stmy5YtJj093dSrV8988803VTfQAE4bYQZApfTo0cN0797dp+3SSy81jz766C++9/gwUxpK1qxZ49MnKSnJzJgxw6ftqaeeMl26dPF536RJk37x+6655hrz8MMP+9R+/CnixphyYebWW281ffr08enzu9/9zrRp08Z9npycbG6//Xb3eUlJiYmNjTVTp079xZoAVD3WzACotPbt2/s8T0hI0O7duwP+3D179mj79u369a9/rbvvvtttLyoqKrdAt1OnTj7Pi4uLNXHiRL377rvasWOHCgsLVVhYqMjIyErV8M0332jgwIE+bd26ddOkSZNUXFyskJAQSb5j4DiO4uPjq2QMAFQeYQZApZ242NZxHJWUlAT8uaWf8frrr6tz584+r5WGiFInhpTnn39eL774oiZNmqR27dopMjJSI0eO1NGjRytVgzFGjuOUaztRdY0BgMojzACwIiwsTNKxGZVScXFxatq0qbZu3arbbrutUp/3+eefa+DAgbr99tslHQtGW7Zs0QUXXODzncd/X0XatGmjJUuW+LQtXbpUrVq1KheoAAQHwgwAK2JjYxUeHq758+erWbNmatCggbxer9LS0jRixAhFR0crNTVVhYWFWrlypfbv369Ro0ad9PPOPfdczZo1S0uXLlWjRo30wgsvKDc31yfMtGjRQl988YW2bdumhg0bKiYmptznPPzww7r00kv11FNP6aabbtKyZcs0efJknx1VAIILW7MBWBEaGqqXXnpJr776qhITE911Kv/zP/+jN954Q9OmTVO7du3Uo0cPTZs2zd3KfTL/7//9P11yySXq16+fevbsqfj4eA0aNMinzyOPPKKQkBC1adNGZ599tr777rtyn3PJJZfovffe08yZM9W2bVs98cQTGjdunIYNG1ZVPzqAKuaYii4GAwAA1BLMzAAAgFqNMAOgSrz99ttq2LBhhY8LL7zQdnkAzmBcZgJQJQoKCrRr164KX6tfv76Sk5NruCIAdQVhBgAA1GpcZgIAALUaYQYAANRqhBkAAFCrEWYAAECtRpgBAAC1GmEGAADUaoQZAABQqxFmAABArfb/AbY41lbU+dSMAAAAAElFTkSuQmCC", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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***************************************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", "text/plain": ["<Figure size 900x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Visualiser le dataset\n", "fig, ax = plt.subplots(figsize=(9, 6))\n", "ax.scatter(X_test[:,0], X_test[:, 1], c=y_test, cmap='winter')\n", "\n", "# Dessiner la frontière de décision\n", "#x1 = np.linspace(0, 10, 200)\n", "x1 = np.linspace(np.min(X_test[:,0]),np.max(X_test[:,0]), 320)\n", "x2 = ( - W[0] * x1 - b) / W[1]\n", "ax.plot(x1, x2, c='orange', lw=3)\n", "\n", "# Prédire la classe de nouveaux éléments\n", "\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 123, "id": "d6db1aac", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[[5.48301840e-01]\n", " [9.11102811e-01]\n", " [8.90117220e-01]\n", " [9.93375232e-01]\n", " [9.99364735e-01]\n", " [2.19128848e-01]\n", " [9.42303597e-01]\n", " [3.05931868e-01]\n", " [9.87894625e-01]\n", " [9.95546320e-01]\n", " [9.51357937e-01]\n", " [7.23651595e-01]\n", " [9.93545040e-01]\n", " [4.40367469e-01]\n", " [1.58167630e-03]\n", " [2.17477294e-03]\n", " [9.99903280e-01]\n", " [9.02660423e-01]\n", " [5.55666813e-01]\n", " [9.54665765e-01]\n", " [6.58407785e-01]\n", " [9.97727540e-01]\n", " [9.97180328e-01]\n", " [9.52553300e-01]\n", " [3.89013177e-01]\n", " [9.97008036e-01]\n", " [9.96501808e-01]\n", " [9.42303597e-01]\n", " [3.14609743e-01]\n", " [9.98485407e-01]\n", " [9.90543745e-01]\n", " [1.62467073e-02]\n", " [9.99792511e-01]\n", " [8.38813514e-01]\n", " [9.79750727e-01]\n", " [1.39835135e-02]\n", " [9.71589820e-01]\n", " [8.53357800e-01]\n", " [9.97827463e-01]\n", " [8.92389444e-01]\n", " [9.89314222e-01]\n", " [9.93053409e-01]\n", " [6.97337951e-01]\n", " [9.26952274e-01]\n", " [9.58230356e-01]\n", " [3.47318409e-02]\n", " [3.89910035e-04]\n", " [9.77410519e-01]\n", " [9.95150129e-01]\n", " [2.83141614e-02]\n", " [9.58122457e-01]\n", " [1.91399228e-03]\n", " [2.73262794e-03]\n", " [9.97016057e-01]\n", " [9.97817653e-01]\n", " [9.90543745e-01]\n", " [3.40204382e-01]\n", " [9.81463696e-01]\n", " [9.94371818e-01]\n", " [9.53720716e-01]\n", " [7.75749751e-02]\n", " [6.14903810e-02]\n", " [4.77501666e-01]\n", " [9.37353890e-03]\n", " [2.13024901e-01]\n", " [2.76978986e-03]\n", " [5.06905063e-02]\n", " [9.89753027e-01]\n", " [9.93727327e-01]\n", " [9.75890226e-01]\n", " [9.03057772e-02]\n", " [8.77445570e-01]\n", " [1.10544343e-03]\n", " [9.68336936e-01]\n", " [9.91962835e-01]\n", " [2.17904630e-02]\n", " [3.13922553e-02]\n", " [2.38581248e-03]\n", " [9.99777800e-01]\n", " [1.77367547e-02]\n", " [4.66281124e-03]\n", " [4.45252633e-01]\n", " [4.05625307e-01]\n", " [6.78380969e-01]\n", " [9.31800796e-01]\n", " [8.78700573e-01]\n", " [9.91351747e-01]\n", " [9.97380655e-01]\n", " [9.41910494e-01]\n", " [8.96222187e-01]\n", " [8.86542597e-01]\n", " [4.45702844e-01]\n", " [9.92650481e-01]\n", " [1.01555636e-04]\n", " [8.02575191e-01]\n", " [8.55710845e-01]\n", " [7.13076470e-01]\n", " [8.91695348e-01]\n", " [7.69906583e-01]\n", " [9.92670098e-01]\n", " [4.67001955e-05]\n", " [9.96764776e-01]\n", " [3.02311435e-01]\n", " [9.93392928e-01]\n", " [9.95939531e-01]\n", " [9.93040825e-01]\n", " [9.95609779e-01]\n", " [7.94660605e-02]\n", " [9.87894625e-01]\n", " [8.73017619e-01]\n", " [1.56391580e-02]\n", " [9.36349597e-01]\n", " [9.90226246e-01]\n", " [8.93079625e-01]\n", " [2.52378624e-01]\n", " [1.27597084e-02]\n", " [9.99545612e-01]\n", " [2.31362437e-02]\n", " [4.32787487e-02]\n", " [9.83391141e-01]\n", " [4.07876861e-04]\n", " [7.46798917e-01]\n", " [9.98770836e-01]\n", " [9.25348803e-01]\n", " [3.69063863e-04]\n", " [6.57391608e-01]\n", " [3.90495214e-02]\n", " [5.91794406e-01]\n", " [9.99202160e-01]\n", " [9.99568744e-01]\n", " [9.98120346e-01]\n", " [2.80182406e-01]\n", " [2.04096541e-01]\n", " [7.62644237e-01]\n", " [9.38152196e-01]\n", " [9.84223716e-01]\n", " [5.64898115e-02]\n", " [9.90433309e-01]\n", " [8.86813137e-01]\n", " [1.02045069e-01]\n", " [8.81458986e-01]\n", " [9.95997410e-01]\n", " [9.91998751e-01]\n", " [9.58410694e-01]\n", " [5.63883675e-01]\n", " [3.72858238e-01]\n", " [9.98261857e-01]\n", " [9.75890226e-01]\n", " [9.80122891e-01]\n", " [9.99392712e-01]\n", " [9.92972075e-01]\n", " [9.48880993e-01]\n", " [9.50474414e-01]\n", " [9.12263441e-01]\n", " [5.13085784e-01]\n", " [9.83273010e-01]\n", " [9.70448789e-01]\n", " [4.67394731e-01]\n", " [5.48465478e-03]\n", " [9.86386175e-01]\n", " [9.98481329e-01]\n", " [9.93871788e-01]\n", " [6.49842507e-01]\n", " [3.11900798e-01]\n", " [4.49717782e-01]\n", " [7.58214743e-01]\n", " [9.98438336e-01]\n", " [9.97292081e-01]\n", " [7.21075031e-05]\n", " [6.95349118e-02]\n", " [2.62016492e-01]\n", " [5.34018617e-02]\n", " [9.99258950e-01]\n", " [9.97392424e-01]\n", " [8.70292669e-02]\n", " [9.35757016e-01]\n", " [4.59103758e-01]\n", " [9.69128586e-01]\n", " [9.88827130e-01]\n", " [8.58360075e-01]\n", " [2.68527621e-05]\n", " [1.62555296e-04]\n", " [9.45314671e-01]\n", " [1.89529517e-02]\n", " [6.73938087e-03]\n", " [6.70065544e-01]\n", " [1.00810592e-01]\n", " [6.14561420e-03]\n", " [7.55722934e-01]\n", " [1.32824025e-03]\n", " [3.87940586e-01]\n", " [9.52020678e-01]\n", " [9.64231282e-01]\n", " [4.39704033e-01]\n", " [4.22244088e-01]\n", " [9.98190053e-01]\n", " [9.91692583e-01]\n", " [8.27928163e-01]\n", " [3.85798667e-01]\n", " [3.04196854e-02]\n", " [6.51480767e-01]\n", " [7.99496668e-03]\n", " [8.86153855e-02]\n", " [1.59755350e-02]\n", " [9.88065804e-01]\n", " [8.56374450e-01]\n", " [9.96796545e-01]\n", " [6.06516553e-01]\n", " [1.00402257e-02]\n", " [9.93231409e-01]\n", " [4.97762615e-01]\n", " [6.48504479e-02]\n", " [7.49514928e-01]\n", " [2.19440860e-01]\n", " [2.83883321e-02]\n", " [9.57781664e-02]\n", " [9.73186451e-01]\n", " [9.19014530e-01]\n", " [1.65146055e-03]\n", " [2.66049030e-01]\n", " [9.27134377e-01]\n", " [8.20352809e-01]\n", " [9.83317236e-01]\n", " [6.27910052e-03]\n", " [9.64231282e-01]\n", " [9.93297640e-01]\n", " [9.47954941e-01]\n", " [8.66798117e-01]\n", " [2.82401789e-02]\n", " [2.07953506e-02]\n", " [9.36188940e-01]\n", " [9.92112358e-01]\n", " [9.58981486e-01]\n", " [9.89633453e-01]\n", " [3.77619757e-03]\n", " [9.95128289e-01]\n", " [1.08307605e-02]\n", " [6.46971762e-01]\n", " [3.58672538e-02]\n", " [3.78356611e-01]\n", " [7.69429264e-01]\n", " [7.45944226e-01]\n", " [9.82638794e-01]\n", " [9.83812692e-01]\n", " [2.28525992e-01]\n", " [3.98357413e-02]\n", " [6.20618719e-01]\n", " [9.83435060e-01]\n", " [8.92389444e-01]\n", " [9.99517777e-01]\n", " [5.85419022e-02]\n", " [2.91697870e-02]\n", " [1.48245310e-01]\n", " [9.82187226e-01]\n", " [8.71108997e-01]\n", " [1.84730222e-02]\n", " [8.67108680e-01]\n", " [8.86542597e-01]\n", " [5.79552377e-01]\n", " [9.69128586e-01]\n", " [6.44906596e-01]\n", " [9.99192760e-01]\n", " [9.99422574e-01]\n", " [7.92980490e-01]\n", " [9.96244608e-01]\n", " [9.27922346e-01]\n", " [9.35048701e-01]\n", " [2.06211689e-04]\n", " [8.53357800e-01]\n", " [9.88150494e-01]\n", " [3.53343889e-03]\n", " [5.38702011e-03]\n", " [1.20195205e-03]\n", " [1.19781791e-02]\n", " [8.65270358e-02]\n", " [6.10174849e-03]\n", " [8.26251722e-01]\n", " [9.91148410e-01]\n", " [9.86578394e-01]\n", " [8.65270358e-02]\n", " [3.75559349e-02]\n", " [3.29424478e-03]\n", " [5.73170544e-01]\n", " [9.27922346e-01]\n", " [9.80436775e-01]\n", " [6.58407785e-01]\n", " [6.01020591e-02]\n", " [9.44186083e-01]\n", " [2.88408907e-03]\n", " [9.75440798e-01]\n", " [9.99073517e-01]\n", " [4.47584912e-02]\n", " [4.58096602e-02]\n", " [9.93844228e-01]\n", " [9.86129620e-01]\n", " [4.70898354e-03]\n", " [9.75890226e-01]\n", " [7.81047466e-01]\n", " [8.21412550e-01]\n", " [9.73490652e-01]\n", " [6.17859222e-03]\n", " [4.39472811e-03]\n", " [9.52020678e-01]\n", " [4.73716523e-02]\n", " [7.25090616e-01]\n", " [9.82060688e-01]\n", " [9.87980515e-01]\n", " [9.82958801e-01]\n", " [9.97542635e-01]\n", " [9.96892948e-01]\n", " [4.34660809e-02]\n", " [3.57400664e-01]\n", " [9.99964846e-01]\n", " [8.44814209e-01]\n", " [9.77250836e-01]\n", " [9.85434830e-01]\n", " [7.51664276e-03]\n", " [8.64281757e-01]\n", " [5.71178607e-02]\n", " [9.97967338e-01]\n", " [8.27928163e-01]\n", " [9.95986661e-01]\n", " [2.99504012e-03]\n", " [3.06574037e-05]\n", " [3.45562503e-04]\n", " [4.71683178e-02]\n", " [9.38413650e-01]\n", " [6.37856382e-01]\n", " [7.55369571e-02]\n", " [6.27808702e-01]\n", " [1.43413995e-04]\n", " [8.84162940e-01]\n", " [4.99181754e-03]\n", " [9.66227082e-01]\n", " [9.76620877e-01]\n", " [5.34940650e-02]\n", " [1.87227517e-01]\n", " [9.98463450e-01]\n", " [4.83170045e-02]\n", " [9.72807723e-01]\n", " [1.48107225e-03]\n", " [9.43067968e-02]\n", " [1.59738741e-03]\n", " [9.28702450e-01]\n", " [7.49172609e-01]\n", " [4.03556701e-03]\n", " [9.87489303e-01]\n", " [1.22665310e-01]\n", " [9.43805041e-01]\n", " [4.82898730e-01]\n", " [9.23841359e-01]\n", " [8.03278097e-02]\n", " [1.01220604e-01]\n", " [7.33982358e-01]\n", " [9.77509992e-01]\n", " [4.15881069e-01]\n", " [9.97868115e-01]\n", " [9.70986852e-01]\n", " [9.93345453e-01]\n", " [2.65830795e-02]\n", " [9.93665315e-01]\n", " [3.74094943e-04]\n", " [2.47980690e-01]\n", " [2.14996724e-01]\n", " [6.88711227e-01]\n", " [9.79170784e-01]\n", " [9.83998320e-01]\n", " [9.99159380e-01]\n", " [9.87160565e-03]\n", " [9.72495906e-01]\n", " [1.11641254e-02]\n", " [2.21392750e-02]\n", " [8.14705436e-01]\n", " [7.96070837e-01]\n", " [1.68402591e-01]\n", " [4.25551868e-01]\n", " [4.53287591e-01]\n", " [2.13343028e-02]\n", " [9.55170406e-01]\n", " [6.08235317e-01]\n", " [1.14931774e-01]\n", " [4.66270952e-01]\n", " [9.93871788e-01]\n", " [4.04640327e-03]\n", " [5.84415356e-02]\n", " [6.13351836e-02]\n", " [6.79365256e-01]\n", " [9.88391041e-01]\n", " [1.44084099e-01]\n", " [2.91400927e-01]\n", " [2.90469522e-01]\n", " [1.62432993e-01]\n", " [7.32572714e-01]\n", " [9.97913621e-01]\n", " [4.32050729e-03]\n", " [7.96507590e-01]\n", " [9.91883521e-01]\n", " [9.94002086e-01]\n", " [8.50056715e-01]\n", " [8.77641419e-01]\n", " [8.94873807e-01]\n", " [3.73235400e-03]\n", " [5.81962789e-01]\n", " [4.33936465e-01]\n", " [9.94999922e-01]\n", " [6.37234203e-01]\n", " [1.97161637e-01]\n", " [9.87633300e-01]\n", " [7.48159351e-01]\n", " [3.49240400e-03]\n", " [9.99468502e-01]\n", " [6.98275989e-02]\n", " [9.90407764e-01]\n", " [9.38828880e-01]\n", " [3.99388529e-02]\n", " [9.90543745e-01]\n", " [9.96254668e-01]\n", " [9.87894625e-01]\n", " [2.35754016e-01]\n", " [9.99451967e-01]\n", " [9.95891849e-01]\n", " [1.89367828e-02]\n", " [9.99756192e-01]\n", " [1.43642686e-03]\n", " [1.18796114e-03]\n", " [9.92754902e-01]\n", " [8.97555252e-01]\n", " [9.83273010e-01]\n", " [3.20083514e-01]\n", " [2.80430581e-02]\n", " [6.95244422e-01]\n", " [9.88666746e-01]\n", " [9.59440127e-01]\n", " [1.69414359e-01]\n", " [9.60718037e-01]\n", " [4.41480458e-01]\n", " [5.45994879e-02]\n", " [9.07157830e-01]\n", " [6.12308397e-04]\n", " [5.97450514e-02]\n", " [9.89034345e-01]\n", " [1.23443078e-01]\n", " [7.86244219e-01]\n", " [7.74571460e-04]\n", " [9.39857337e-03]\n", " [9.08739912e-01]\n", " [7.14548836e-01]\n", " [1.56525563e-02]\n", " [9.58662572e-01]\n", " [9.96117768e-01]\n", " [1.25506029e-01]\n", " [7.95774806e-01]\n", " [9.39651499e-01]\n", " [7.11599573e-01]\n", " [5.07751180e-03]\n", " [9.97272547e-01]\n", " [9.96244608e-01]\n", " [3.53064953e-01]\n", " [9.96162838e-01]\n", " [9.94264879e-01]\n", " [4.49892906e-02]\n", " [9.91451673e-01]\n", " [3.42713938e-03]\n", " [8.87534575e-01]\n", " [9.91021045e-01]\n", " [9.27438805e-01]\n", " [2.90421989e-02]\n", " [6.68470188e-01]\n", " [9.99722163e-01]\n", " [1.75850718e-01]\n", " [5.38023829e-01]\n", " [4.49717782e-01]\n", " [9.89131847e-01]\n", " [1.40020490e-01]\n", " [1.80850252e-02]\n", " [1.44125046e-02]\n", " [8.79180985e-01]\n", " [9.92369059e-01]\n", " [8.70091727e-01]\n", " [9.90826585e-01]\n", " [8.85451506e-02]\n", " [4.38296314e-03]\n", " [9.90269846e-01]\n", " [5.28601177e-01]\n", " [4.83600266e-04]\n", " [9.88876902e-01]\n", " [5.66764255e-01]\n", " [8.72216465e-01]\n", " [3.87301471e-01]\n", " [9.52877984e-01]\n", " [9.99309846e-01]\n", " [1.05566874e-01]\n", " [9.97952665e-01]\n", " [9.94770632e-01]\n", " [2.56207663e-02]\n", " [9.98207851e-01]\n", " [9.76662453e-01]\n", " [6.31170710e-01]\n", " [9.35048701e-01]\n", " [9.99153304e-01]\n", " [3.10542276e-01]\n", " [4.17189790e-01]\n", " [1.03887515e-03]\n", " [4.46153144e-01]\n", " [9.72807723e-01]\n", " [9.59158720e-01]\n", " [9.97560240e-01]\n", " [8.53694410e-01]\n", " [6.55158407e-01]\n", " [9.93297640e-01]\n", " [9.96711990e-01]\n", " [9.68775934e-01]\n", " [1.22469309e-01]\n", " [6.89678360e-01]\n", " [9.95910279e-01]\n", " [9.80349987e-01]\n", " [8.08149946e-03]\n", " [8.51884789e-01]\n", " [9.82638794e-01]\n", " [4.10590769e-02]\n", " [2.12781587e-02]\n", " [5.53483576e-02]\n", " [7.72812570e-03]\n", " [9.89679670e-01]\n", " [4.47833616e-03]\n", " [9.90041327e-01]\n", " [8.53693910e-04]\n", " [5.69193483e-01]\n", " [9.94223634e-01]\n", " [1.58206484e-02]\n", " [7.91683110e-03]\n", " [8.45166873e-01]\n", " [9.99902318e-01]\n", " [9.92348643e-01]\n", " [7.74880004e-03]\n", " [8.94181935e-02]\n", " [1.93772942e-02]\n", " [9.98956596e-01]\n", " [9.17113937e-01]\n", " [8.69274878e-01]\n", " [9.78231496e-01]\n", " [1.24901836e-02]\n", " [9.10884494e-01]\n", " [6.58407785e-01]\n", " [9.64231282e-01]\n", " [3.78634002e-03]\n", " [3.11323236e-01]\n", " [9.03057772e-02]\n", " [9.99960512e-01]\n", " [6.28573603e-02]\n", " [9.65753516e-01]\n", " [9.96100269e-01]\n", " [9.59719677e-01]\n", " [9.55170406e-01]\n", " [7.49514928e-01]\n", " [9.56464776e-01]\n", " [7.60460064e-03]\n", " [1.54264188e-01]\n", " [8.26251722e-01]\n", " [8.69787088e-01]\n", " [9.84445987e-01]\n", " [9.97843031e-01]\n", " [4.75703750e-01]\n", " [7.86244219e-01]\n", " [1.33531658e-01]\n", " [9.98373810e-01]\n", " [9.97448058e-01]\n", " [2.00899067e-01]\n", " [4.09545966e-01]\n", " [3.66225227e-02]\n", " [9.55478018e-01]\n", " [9.91021045e-01]\n", " [9.77667898e-01]\n", " [9.92940496e-01]\n", " [7.48159351e-01]\n", " [9.77980467e-01]\n", " [9.58590288e-01]\n", " [9.95308434e-01]\n", " [8.47627882e-01]\n", " [1.31872632e-01]\n", " [6.64261689e-01]\n", " [1.09805358e-01]\n", " [1.72516078e-04]\n", " [9.96679386e-01]\n", " [8.56598468e-01]\n", " [9.79371742e-01]\n", " [9.89437438e-01]\n", " [1.42091706e-02]\n", " [8.91955098e-01]\n", " [4.46331239e-04]\n", " [4.10491309e-03]\n", " [9.57171589e-01]\n", " [2.34642839e-02]\n", " [9.61698997e-02]\n", " [8.39785623e-01]\n", " [9.96145541e-01]\n", " [9.84692670e-01]\n", " [9.98797097e-01]\n", " [2.40075378e-02]\n", " [9.94044904e-01]\n", " [6.21703627e-02]\n", " [1.07709575e-01]\n", " [4.15231185e-02]\n", " [5.57445622e-01]\n", " [2.58888914e-01]\n", " [9.77726608e-01]\n", " [9.91250664e-01]\n", " [2.52185168e-02]\n", " [1.35123388e-05]\n", " [3.90137927e-02]\n", " [8.41486536e-04]\n", " [7.32411578e-02]\n", " [8.79944484e-01]\n", " [7.64759673e-01]\n", " [1.51464228e-01]\n", " [9.15596348e-01]\n", " [1.33320932e-01]\n", " [1.12031531e-01]\n", " [4.02161320e-02]\n", " [5.08355519e-01]\n", " [2.74578261e-01]\n", " [7.33740568e-03]\n", " [9.95578163e-01]\n", " [1.25793948e-02]\n", " [8.50399575e-01]\n", " [2.19837761e-02]\n", " [9.99633963e-01]\n", " [9.96460705e-01]\n", " [8.99282800e-01]\n", " [3.49779457e-01]\n", " [9.75890226e-01]\n", " [6.17218858e-01]\n", " [9.81199621e-01]\n", " [9.01625563e-01]\n", " [9.97711140e-01]\n", " [5.21127342e-03]\n", " [9.88717224e-01]\n", " [9.71911612e-01]\n", " [9.97342725e-01]\n", " [1.66514222e-01]\n", " [6.81517898e-01]\n", " [7.67402938e-02]\n", " [6.07158939e-01]\n", " [1.96238545e-04]\n", " [9.95287304e-01]\n", " [9.99337256e-01]\n", " [9.57061078e-01]\n", " [9.41763000e-01]\n", " [1.49963775e-01]\n", " [8.40677665e-02]\n", " [8.66275957e-01]\n", " [9.98708436e-01]\n", " [1.54854164e-01]\n", " [1.71797449e-02]\n", " [9.83435060e-01]\n", " [9.48530256e-01]\n", " [6.83080235e-01]\n", " [9.95093223e-01]\n", " [9.99057546e-01]\n", " [2.11671417e-04]\n", " [3.55890704e-03]\n", " [8.86087664e-01]\n", " [8.50399575e-01]\n", " [7.49009098e-01]\n", " [4.62685427e-01]\n", " [7.15468860e-01]\n", " [9.95880818e-01]\n", " [7.40442011e-01]\n", " [2.59156134e-03]\n", " [4.31700123e-04]\n", " [9.61858626e-01]\n", " [4.59638606e-03]\n", " [9.91535170e-01]\n", " [7.81047466e-01]\n", " [9.98971514e-01]\n", " [2.59853029e-03]\n", " [9.52020678e-01]\n", " [9.97996370e-01]\n", " [9.99744956e-01]\n", " [9.98241398e-01]\n", " [9.93755408e-01]\n", " [9.23012476e-01]\n", " [9.02262994e-01]\n", " [1.74779202e-03]\n", " [3.74118286e-01]\n", " [9.92735510e-01]\n", " [9.86732735e-01]\n", " [9.97349851e-01]\n", " [9.90475995e-01]\n", " [5.07751180e-03]\n", " [9.95832450e-01]\n", " [1.56748038e-03]\n", " [8.27283991e-01]\n", " [9.53200556e-01]\n", " [9.84335239e-01]\n", " [8.26637913e-01]\n", " [9.97749876e-01]\n", " [9.63318803e-01]\n", " [9.74806808e-01]\n", " [1.25126828e-02]\n", " [9.71589820e-01]\n", " [9.98318764e-01]\n", " [8.44221358e-01]\n", " [2.56480213e-01]\n", " [9.65753516e-01]\n", " [9.58981486e-01]\n", " [6.00261705e-01]\n", " [1.64773889e-01]\n", " [2.60274178e-01]\n", " [9.93451932e-01]\n", " [2.10762979e-01]\n", " [1.32455283e-02]\n", " [6.94286946e-01]\n", " [7.04163941e-02]\n", " [9.97807799e-01]\n", " [5.14430999e-01]\n", " [8.83422715e-01]\n", " [9.99912014e-01]\n", " [4.40816666e-01]\n", " [1.97571297e-02]\n", " [2.86343616e-03]\n", " [9.97858488e-01]\n", " [2.92979186e-02]\n", " [1.39696591e-01]\n", " [8.85358143e-01]\n", " [1.16129603e-01]\n", " [9.45453691e-01]\n", " [7.14867048e-02]\n", " [9.83581522e-01]\n", " [9.99961807e-01]\n", " [9.98866483e-01]\n", " [9.07763081e-01]\n", " [1.88326773e-01]\n", " [1.23734712e-01]\n", " [5.35108900e-01]\n", " [8.23517343e-01]\n", " [5.04736810e-02]\n", " [9.99101459e-01]\n", " [9.95022334e-01]\n", " [9.37310503e-01]\n", " [9.82958801e-01]\n", " [9.18878783e-01]\n", " [9.97448058e-01]\n", " [2.80430581e-02]\n", " [9.37044680e-01]\n", " [9.77090050e-01]\n", " [8.27928163e-01]\n", " [9.97889072e-01]\n", " [9.68557176e-01]\n", " [1.10691136e-01]\n", " [9.78536350e-01]\n", " [9.95832450e-01]\n", " [9.99217109e-01]\n", " [9.81804961e-01]\n", " [9.67069689e-01]\n", " [9.97605880e-01]\n", " [9.02660423e-01]\n", " [2.46485524e-02]\n", " [9.83927074e-01]\n", " [9.86920126e-01]\n", " [1.32181177e-01]\n", " [6.71656995e-01]\n", " [2.19589909e-01]\n", " [6.20618719e-01]\n", " [9.65842454e-01]\n", " [9.97566784e-01]\n", " [1.84141484e-03]\n", " [8.38813514e-01]\n", " [9.77726608e-01]\n", " [7.93115826e-03]\n", " [7.42854778e-01]\n", " [9.84514969e-01]\n", " [9.96004669e-01]\n", " [2.97770448e-01]\n", " [7.99126737e-01]\n", " [9.92972075e-01]\n", " [8.35144507e-02]\n", " [9.97495792e-01]\n", " [4.76829952e-01]\n", " [9.75093097e-01]\n", " [9.94770632e-01]\n", " [9.91751751e-01]\n", " [6.88413245e-02]\n", " [1.27826869e-02]\n", " [9.95461596e-01]\n", " [9.62644003e-01]\n", " [7.36787393e-01]\n", " [9.99963752e-01]\n", " [7.18025364e-01]\n", " [9.11320645e-01]\n", " [9.86920126e-01]\n", " [9.98555941e-01]\n", " [8.81458986e-01]\n", " [9.99207885e-01]\n", " [9.79697241e-01]\n", " [9.99049005e-01]\n", " [9.45686066e-01]\n", " [2.92330472e-03]\n", " [7.29379653e-01]\n", " [1.03958596e-02]\n", " [2.79484403e-03]\n", " [5.84157980e-01]\n", " [9.85187238e-01]\n", " [2.37343944e-02]\n", " [8.28311402e-01]\n", " [4.12383641e-01]\n", " [4.24749979e-04]\n", " [9.98744003e-01]\n", " [9.19748301e-01]\n", " [3.05765181e-03]\n", " [9.74156885e-01]\n", " [5.93973989e-01]\n", " [9.31224717e-01]\n", " [3.94227128e-02]\n", " [1.48706153e-01]\n", " [9.23523082e-01]\n", " [9.70032584e-01]\n", " [9.99084304e-01]\n", " [9.62082295e-05]\n", " [3.02474793e-03]\n", " [7.18394213e-01]\n", " [4.67394731e-01]\n", " [4.56194181e-01]\n", " [7.69013117e-04]\n", " [9.47239152e-01]\n", " [9.52877984e-01]\n", " [8.30965388e-04]\n", " [9.28402915e-01]\n", " [9.86885324e-01]\n", " [9.98670709e-01]\n", " [9.40463758e-01]\n", " [4.19062930e-03]\n", " [9.49919683e-01]\n", " [9.96711990e-01]\n", " [7.26524975e-01]\n", " [2.76236288e-03]\n", " [9.91632994e-01]\n", " [7.31687256e-01]\n", " [9.76435737e-01]\n", " [3.35968216e-01]\n", " [2.16893798e-03]\n", " [9.07763081e-01]\n", " [9.94223634e-01]\n", " [9.29178214e-01]\n", " [1.32455283e-02]\n", " [9.77410519e-01]\n", " [9.98846947e-01]\n", " [1.20317305e-02]\n", " [2.72610750e-01]\n", " [8.83145139e-01]\n", " [9.78135143e-01]\n", " [9.65693188e-01]\n", " [8.44221358e-01]\n", " [9.94182094e-01]\n", " [9.48530256e-01]\n", " [2.88933496e-03]\n", " [7.64759673e-01]\n", " [9.99521238e-01]\n", " [3.01743844e-01]\n", " [2.28320840e-02]\n", " [9.64479033e-01]\n", " [6.35919668e-04]\n", " [3.86120098e-02]\n", " [7.04708379e-01]\n", " [9.98449533e-01]\n", " [1.40670174e-01]\n", " [4.76837133e-03]\n", " [3.37985719e-01]\n", " [9.31341348e-01]\n", " [3.27086554e-04]\n", " [7.47651705e-01]\n", " [9.98928994e-01]\n", " [3.39703103e-04]\n", " [9.99596555e-01]\n", " [9.87309962e-01]\n", " [9.99641790e-01]\n", " [7.71416245e-03]\n", " [2.70823841e-01]\n", " [9.34773955e-01]\n", " [1.27441418e-03]\n", " [9.82638794e-01]\n", " [7.66053836e-01]\n", " [1.09018291e-01]\n", " [1.08214442e-02]\n", " [1.03586251e-04]\n", " [9.47013049e-01]\n", " [9.47373552e-01]\n", " [7.05357856e-02]\n", " [9.22820930e-01]\n", " [1.68147514e-01]\n", " [9.98042708e-01]\n", " [9.99585876e-01]\n", " [9.99369294e-01]\n", " [3.04789638e-01]\n", " [6.07158939e-01]\n", " [9.82515408e-01]\n", " [9.94831261e-01]\n", " [9.02843656e-03]\n", " [9.34167736e-01]\n", " [9.67525667e-01]\n", " [9.95086893e-03]\n", " [9.46650217e-01]\n", " [9.07537395e-01]\n", " [8.94193835e-01]\n", " [7.34241245e-02]\n", " [2.82160368e-04]\n", " [9.27922346e-01]\n", " [6.34942573e-01]\n", " [8.78190901e-02]\n", " [3.76662888e-01]\n", " [9.99533584e-01]\n", " [1.15634347e-03]\n", " [2.76919356e-01]\n", " [6.87163936e-01]\n", " [9.14121895e-01]\n", " [3.37187323e-02]\n", " [1.10931114e-04]\n", " [6.27500882e-02]\n", " [6.35566436e-01]\n", " [8.33750617e-02]\n", " [1.54264188e-01]\n", " [9.89160753e-01]\n", " [9.51357937e-01]\n", " [9.92787304e-01]\n", " [8.10727232e-01]\n", " [2.16297095e-04]\n", " [5.17852189e-03]\n", " [8.94957836e-02]\n", " [9.96616143e-01]\n", " [9.67383507e-01]\n", " [1.77050304e-02]\n", " [8.35618405e-01]\n", " [9.91474462e-01]\n", " [6.89678360e-01]\n", " [9.90226246e-01]\n", " [9.27922346e-01]\n", " [1.90405998e-01]\n", " [1.04234208e-05]\n", " [3.21968168e-02]\n", " [9.87980515e-01]\n", " [2.16672280e-01]\n", " [9.69047929e-01]\n", " [1.93426935e-02]\n", " [9.55452393e-02]\n", " [5.96961998e-02]\n", " [9.77667898e-01]\n", " [9.64878455e-01]\n", " [9.97721427e-01]\n", " [5.14639802e-03]\n", " [9.99370989e-01]\n", " [4.34660809e-02]\n", " [9.23332701e-01]\n", " [9.85830740e-01]\n", " [4.07762329e-02]\n", " [9.17594416e-01]\n", " [8.94800324e-03]\n", " [9.85931065e-01]\n", " [9.12624144e-01]\n", " [9.91312952e-01]\n", " [9.25162598e-01]\n", " [8.11830730e-01]\n", " [2.93449341e-01]\n", " [6.37118595e-02]\n", " [9.30827548e-02]\n", " [5.90415753e-02]\n", " [9.95558242e-01]\n", " [9.90956684e-01]\n", " [1.23734712e-01]\n", " [9.99153304e-01]\n", " [9.77410519e-01]\n", " [9.52348825e-01]\n", " [1.36833843e-03]\n", " [3.91802988e-01]\n", " [9.98816448e-01]\n", " [1.54597169e-02]\n", " [8.47627882e-01]\n", " [8.14298635e-01]\n", " [2.15419874e-02]\n", " [9.88234591e-01]\n", " [7.53890727e-01]\n", " [2.04356123e-05]\n", " [9.48661545e-01]\n", " [9.97220569e-01]\n", " [6.15083194e-01]\n", " [9.60203322e-03]\n", " [7.31175473e-02]\n", " [7.37318848e-02]\n", " [8.51843960e-02]\n", " [9.35757016e-01]\n", " [9.35048701e-01]\n", " [5.99319487e-03]\n", " [2.39181736e-01]\n", " [7.85484431e-01]\n", " [2.78123348e-04]\n", " [9.97167604e-01]\n", " [9.05255777e-03]\n", " [8.65270358e-02]\n", " [7.39573345e-01]\n", " [8.05978818e-01]\n", " [9.11467822e-01]]\n", "[[343  48]\n", " [ 44 565]]\n"]}], "source": ["#Affichage de la matrice de confusion\n", "\n", "from sklearn.metrics import confusion_matrix\n", "y_pred = predict(X_test, W, b)\n", "cm = confusion_matrix(y_test, y_pred)\n", "print(cm)"]}, {"cell_type": "code", "execution_count": 124, "id": "1abc5628", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Plot confusion matrix\n", "import seaborn as sns\n", "import pandas as pd\n", "# confusion matrix sns heatmap \n", "## https://www.kaggle.com/agungor2/various-confusion-matrix-plots\n", "ax = plt.axes()\n", "df_cm = cm\n", "sns.heatmap(df_cm, annot=True, annot_kws={\"size\": 30}, fmt='d',cmap=\"Blues\", ax = ax )\n", "ax.set_title('Confusion Matrix')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "d817486b-e7f8-44d9-8ee9-961d62149959", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 5}