{"cells": [{"cell_type": "markdown", "id": "a1b2c3d4", "metadata": {}, "source": ["# Comparaison des Modèles de Classification - Prédiction d'Admissions\n", "\n", "Comparaison des performances entre :\n", "1. **R<PERSON>gression Logistique Linéaire** (from scratch)\n", "2. **Régression Logistique Non Linéaire** (from scratch)\n", "3. **SVM** (from scratch)\n", "\n", "Pour la classification des admissions selon GPA et GRE."]}, {"cell_type": "code", "execution_count": null, "id": "i5j6k7l8", "metadata": {}, "outputs": [], "source": ["#souhail ouargui\n", "import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.preprocessing import StandardScaler\n", "from sklearn.metrics import accuracy_score, confusion_matrix, classification_report, precision_score, recall_score, f1_score\n", "import seaborn as sns\n", "import time\n", "\n", "%matplotlib inline"]}, {"cell_type": "markdown", "id": "e1f2g3h4", "metadata": {}, "source": ["### 1. Chargement et préparation des données"]}, {"cell_type": "code", "execution_count": null, "id": "m9n0o1p2", "metadata": {}, "outputs": [], "source": ["#souhail ouargui\n", "# Chargement du dataset\n", "dataset = pd.read_csv('admissions - admissions.csv')\n", "print(\"Dataset d'admissions:\")\n", "print(f\"Shape: {dataset.shape}\")\n", "print(f\"Distribution des classes: {dataset['admit'].value_counts().to_dict()}\")\n", "\n", "# Préparation des données\n", "X = dataset[['gpa', 'gre']].values\n", "y = dataset['admit'].values\n", "\n", "# Séparation train/test (même split pour tous les modèles)\n", "X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.33, random_state=42)\n", "\n", "# Normalisation\n", "scaler = StandardScaler()\n", "X_train_scaled = scaler.fit_transform(X_train)\n", "X_test_scaled = scaler.transform(X_test)\n", "\n", "print(f\"\\nTaille train: {X_train.shape[0]}, test: {X_test.shape[0]}\")"]}, {"cell_type": "markdown", "id": "k3l4m5n6", "metadata": {}, "source": ["### 2. Implémentation des modèles"]}, {"cell_type": "code", "execution_count": null, "id": "o7p8q9r0", "metadata": {}, "outputs": [], "source": ["#souhail ouargui\n", "# Fonctions utilitaires\n", "def sigmoid(x):\n", "    x = np.clip(x, -500, 500)\n", "    return 1 / (1 + np.exp(-x))\n", "\n", "def create_polynomial_features(X, degree=2):\n", "    \"\"\"Crée des features polynomiales\"\"\"\n", "    n_samples, n_features = X.shape\n", "    poly_features = [np.ones((n_samples, 1))]  # Biais\n", "    poly_features.append(X)  # Features originales\n", "    \n", "    if degree >= 2:\n", "        # Termes quadratiques\n", "        for i in range(n_features):\n", "            poly_features.append((X[:, i] ** 2).reshape(-1, 1))\n", "        # Termes croisés\n", "        for i in range(n_features):\n", "            for j in range(i+1, n_features):\n", "                poly_features.append((X[:, i] * X[:, j]).reshape(-1, 1))\n", "    \n", "    return np.hstack(poly_features)"]}, {"cell_type": "code", "execution_count": null, "id": "s1t2u3v4", "metadata": {}, "outputs": [], "source": ["#souhail ouargui\n", "# Modèle 1: <PERSON><PERSON><PERSON> Logisti<PERSON> Liné<PERSON>\n", "class LogisticRegressionLinear:\n", "    def __init__(self, learning_rate=0.01, n_iter=1000):\n", "        self.lr = learning_rate\n", "        self.n_iter = n_iter\n", "        self.w = None\n", "        self.b = None\n", "        self.cost_history = []\n", "    \n", "    def fit(self, X, y):\n", "        n_samples, n_features = X.shape\n", "        self.w = np.random.randn(n_features, 1) * 0.01\n", "        self.b = np.random.randn(1) * 0.01\n", "        y = y.reshape(-1, 1)\n", "        \n", "        for i in range(self.n_iter):\n", "            # Forward pass\n", "            Z = X.dot(self.w) + self.b\n", "            A = sigmoid(Z)\n", "            \n", "            # Cost\n", "            epsilon = 1e-15\n", "            A = np.clip(A, epsilon, 1 - epsilon)\n", "            cost = -np.mean(y * np.log(A) + (1 - y) * np.log(1 - A))\n", "            self.cost_history.append(cost)\n", "            \n", "            # Gradients\n", "            dw = np.dot(X.T, A - y) / n_samples\n", "            db = np.sum(A - y) / n_samples\n", "            \n", "            # Update\n", "            self.w -= self.lr * dw\n", "            self.b -= self.lr * db\n", "    \n", "    def predict(self, X):\n", "        A = sigmoid(X.dot(self.w) + self.b)\n", "        return (A >= 0.5).astype(int).ravel()\n", "    \n", "    def predict_proba(self, X):\n", "        return sigmoid(X.dot(self.w) + self.b).ravel()"]}, {"cell_type": "code", "execution_count": null, "id": "w5x6y7z8", "metadata": {}, "outputs": [], "source": ["#souhail ouargui\n", "# Mod<PERSON>le 2: <PERSON><PERSON><PERSON> Logistique Non Linéaire\n", "class LogisticRegressionNonLinear:\n", "    def __init__(self, learning_rate=0.01, n_iter=1000, lambda_reg=0.01, degree=2):\n", "        self.lr = learning_rate\n", "        self.n_iter = n_iter\n", "        self.lambda_reg = lambda_reg\n", "        self.degree = degree\n", "        self.w = None\n", "        self.cost_history = []\n", "    \n", "    def fit(self, X, y):\n", "        # Créer features polynomiales\n", "        X_poly = create_polynomial_features(X, self.degree)\n", "        n_samples, n_features = X_poly.shape\n", "        self.w = np.random.randn(n_features, 1) * 0.001\n", "        y = y.reshape(-1, 1)\n", "        \n", "        for i in range(self.n_iter):\n", "            # Forward pass\n", "            Z = X_poly.dot(self.w)\n", "            A = sigmoid(Z)\n", "            \n", "            # Cost avec régularisation\n", "            epsilon = 1e-15\n", "            A = np.clip(A, epsilon, 1 - epsilon)\n", "            cost = -np.mean(y * np.log(A) + (1 - y) * np.log(1 - A))\n", "            reg_cost = cost + self.lambda_reg * np.sum(self.w[1:] ** 2)\n", "            self.cost_history.append(reg_cost)\n", "            \n", "            # Gradients avec régularisation\n", "            dw = np.dot(X_poly.T, A - y) / n_samples\n", "            dw[1:] += 2 * self.lambda_reg * self.w[1:]\n", "            \n", "            # Update\n", "            self.w -= self.lr * dw\n", "    \n", "    def predict(self, X):\n", "        X_poly = create_polynomial_features(X, self.degree)\n", "        A = sigmoid(X_poly.dot(self.w))\n", "        return (A >= 0.5).astype(int).ravel()\n", "    \n", "    def predict_proba(self, X):\n", "        X_poly = create_polynomial_features(X, self.degree)\n", "        return sigmoid(X_poly.dot(self.w)).ravel()"]}, {"cell_type": "code", "execution_count": null, "id": "a9b0c1d2", "metadata": {}, "outputs": [], "source": ["#souhail ouargui\n", "# Modèle 3: SVM\n", "class SVM:\n", "    def __init__(self, learning_rate=0.001, lambda_param=0.01, n_iters=1000):\n", "        self.lr = learning_rate\n", "        self.lambda_param = lambda_param\n", "        self.n_iters = n_iters\n", "        self.w = None\n", "        self.b = None\n", "        self.cost_history = []\n", "    \n", "    def fit(self, X, y):\n", "        # Convertir labels en -1/1\n", "        y_svm = np.where(y == 0, -1, 1)\n", "        n_samples, n_features = X.shape\n", "        \n", "        self.w = np.zeros(n_features)\n", "        self.b = 0\n", "        \n", "        for i in range(self.n_iters):\n", "            cost = 0\n", "            dw = np.zeros(n_features)\n", "            db = 0\n", "            \n", "            for idx, x_i in enumerate(X):\n", "                condition = y_svm[idx] * (np.dot(x_i, self.w) - self.b) >= 1\n", "                \n", "                if condition:\n", "                    dw += self.lambda_param * self.w\n", "                else:\n", "                    cost += 1 - y_svm[idx] * (np.dot(x_i, self.w) - self.b)\n", "                    dw += self.lambda_param * self.w - y_svm[idx] * x_i\n", "                    db += y_svm[idx]\n", "            \n", "            total_cost = self.lambda_param * np.dot(self.w, self.w) + cost / n_samples\n", "            self.cost_history.append(total_cost)\n", "            \n", "            self.w -= self.lr * dw / n_samples\n", "            self.b -= self.lr * db / n_samples\n", "    \n", "    def predict(self, X):\n", "        predictions = np.sign(np.dot(X, self.w) - self.b)\n", "        return np.where(predictions == -1, 0, 1)  # Convertir en 0/1\n", "    \n", "    def decision_function(self, X):\n", "        return np.dot(X, self.w) - self.b"]}, {"cell_type": "markdown", "id": "e3f4g5h6", "metadata": {}, "source": ["### 3. Entraînement et évaluation des modèles"]}, {"cell_type": "code", "execution_count": null, "id": "i7j8k9l0", "metadata": {}, "outputs": [], "source": ["#souhail ouargui\n", "# Fonction d'évaluation complète\n", "def evaluate_model(model, X_train, X_test, y_train, y_test, model_name):\n", "    \"\"\"Évalue un modèle et retourne les métriques\"\"\"\n", "    \n", "    # Prédictions\n", "    y_pred_train = model.predict(X_train)\n", "    y_pred_test = model.predict(X_test)\n", "    \n", "    # Métriques\n", "    metrics = {\n", "        'model_name': model_name,\n", "        'accuracy_train': accuracy_score(y_train, y_pred_train),\n", "        'accuracy_test': accuracy_score(y_test, y_pred_test),\n", "        'precision': precision_score(y_test, y_pred_test),\n", "        'recall': recall_score(y_test, y_pred_test),\n", "        'f1_score': f1_score(y_test, y_pred_test),\n", "        'overfitting': accuracy_score(y_train, y_pred_train) - accuracy_score(y_test, y_pred_test)\n", "    }\n", "    \n", "    return metrics, y_pred_test"]}, {"cell_type": "code", "execution_count": null, "id": "m1n2o3p4", "metadata": {}, "outputs": [], "source": ["#souhail ouargui\n", "# Entraînement des trois modèles\n", "print(\"=\" * 60)\n", "print(\"ENTRAÎNEMENT DES MODÈLES DE CLASSIFICATION\")\n", "print(\"=\" * 60)\n", "\n", "# Dictionnaire pour stocker les résultats\n", "results = {}\n", "predictions = {}\n", "training_times = {}\n", "\n", "# 1. <PERSON><PERSON><PERSON> Logistique Linéaire\n", "print(\"\\n1. Entraînement Régression Logistique Linéaire...\")\n", "start_time = time.time()\n", "lr_linear = LogisticRegressionLinear(learning_rate=0.1, n_iter=2000)\n", "lr_linear.fit(X_train_scaled, y_train)\n", "training_times['Logistique Linéaire'] = time.time() - start_time\n", "results['Logistique Linéaire'], predictions['Logistique Linéaire'] = evaluate_model(\n", "    lr_linear, X_train_scaled, X_test_scaled, y_train, y_test, 'Logistique Linéaire')\n", "print(f\"✅ Terminé en {training_times['Logistique Linéaire']:.2f}s\")\n", "\n", "# 2. <PERSON><PERSON>gression Logistique Non Linéaire\n", "print(\"\\n2. Entraînement Régression Logistique Non Linéaire...\")\n", "start_time = time.time()\n", "lr_nonlinear = LogisticRegressionNonLinear(learning_rate=0.01, n_iter=10000, lambda_reg=0.01)\n", "lr_nonlinear.fit(X_train_scaled, y_train)\n", "training_times['Logistique Non Linéaire'] = time.time() - start_time\n", "results['Logistique Non Linéaire'], predictions['Logistique Non Linéaire'] = evaluate_model(\n", "    lr_nonlinear, X_train_scaled, X_test_scaled, y_train, y_test, 'Logistique Non Linéaire')\n", "print(f\"✅ Terminé en {training_times['Logistique Non Linéaire']:.2f}s\")\n", "\n", "# 3. SVM\n", "print(\"\\n3. Entraînement SVM...\")\n", "start_time = time.time()\n", "svm = SVM(learning_rate=0.001, lambda_param=0.01, n_iters=1000)\n", "svm.fit(X_train_scaled, y_train)\n", "training_times['SVM'] = time.time() - start_time\n", "results['SVM'], predictions['SVM'] = evaluate_model(\n", "    svm, X_train_scaled, X_test_scaled, y_train, y_test, 'SVM')\n", "print(f\"✅ Terminé en {training_times['SVM']:.2f}s\")\n", "\n", "print(\"\\n🎉 Tous les modèles ont été entraînés avec succès !\")"]}, {"cell_type": "markdown", "id": "q5r6s7t8", "metadata": {}, "source": ["### 4. Tableau comparatif des performances"]}, {"cell_type": "code", "execution_count": null, "id": "u9v0w1x2", "metadata": {}, "outputs": [], "source": ["#souhail ouargui\n", "# Création du tableau comparatif\n", "comparison_df = pd.DataFrame(results).T\n", "comparison_df['training_time'] = [training_times[model] for model in comparison_df.index]\n", "\n", "# Réorganiser les colonnes\n", "columns_order = ['accuracy_train', 'accuracy_test', 'precision', 'recall', 'f1_score', 'overfitting', 'training_time']\n", "comparison_df = comparison_df[columns_order]\n", "\n", "# A<PERSON><PERSON><PERSON> les valeurs\n", "comparison_df = comparison_df.round(4)\n", "\n", "print(\"=\" * 80)\n", "print(\"TABLEAU COMPARATIF DES PERFORMANCES\")\n", "print(\"=\" * 80)\n", "print(comparison_df.to_string())\n", "\n", "# Identifier le meilleur modèle pour chaque métrique\n", "print(\"\\n\" + \"=\" * 50)\n", "print(\"MEILLEURS MODÈLES PAR MÉTRIQUE\")\n", "print(\"=\" * 50)\n", "print(f\"🏆 Meilleure Accuracy Test: {comparison_df['accuracy_test'].idxmax()} ({comparison_df['accuracy_test'].max():.3f})\")\n", "print(f\"🏆 Meilleure Précision: {comparison_df['precision'].idxmax()} ({comparison_df['precision'].max():.3f})\")\n", "print(f\"🏆 Meilleur Rappel: {comparison_df['recall'].idxmax()} ({comparison_df['recall'].max():.3f})\")\n", "print(f\"🏆 Meilleur F1-Score: {comparison_df['f1_score'].idxmax()} ({comparison_df['f1_score'].max():.3f})\")\n", "print(f\"🏆 Moins d'Overfitting: {comparison_df['overfitting'].idxmin()} ({comparison_df['overfitting'].min():.3f})\")\n", "print(f\"⚡ Plus Rapide: {comparison_df['training_time'].idxmin()} ({comparison_df['training_time'].min():.2f}s)\")"]}, {"cell_type": "markdown", "id": "y3z4a5b6", "metadata": {}, "source": ["### 5. Visualisations comparatives"]}, {"cell_type": "code", "execution_count": null, "id": "c7d8e9f0", "metadata": {}, "outputs": [], "source": ["#souhail ouargui\n", "# Graphiques de comparaison des métriques\n", "fig, axes = plt.subplots(2, 3, figsize=(18, 12))\n", "fig.suptitle('Comparaison des Performances des Modèles', fontsize=16, fontweight='bold')\n", "\n", "# 1. Accuracy\n", "axes[0, 0].bar(comparison_df.index, comparison_df['accuracy_test'], color=['skyblue', 'lightgreen', 'salmon'])\n", "axes[0, 0].set_title('Accuracy Test')\n", "axes[0, 0].set_ylabel('Accuracy')\n", "axes[0, 0].tick_params(axis='x', rotation=45)\n", "for i, v in enumerate(comparison_df['accuracy_test']):\n", "    axes[0, 0].text(i, v + 0.01, f'{v:.3f}', ha='center', va='bottom')\n", "\n", "# 2. <PERSON><PERSON><PERSON>\n", "axes[0, 1].bar(comparison_df.index, comparison_df['precision'], color=['skyblue', 'lightgreen', 'salmon'])\n", "axes[0, 1].set_title('Précision')\n", "axes[0, 1].set_ylabel('Précision')\n", "axes[0, 1].tick_params(axis='x', rotation=45)\n", "for i, v in enumerate(comparison_df['precision']):\n", "    axes[0, 1].text(i, v + 0.01, f'{v:.3f}', ha='center', va='bottom')\n", "\n", "# 3. <PERSON><PERSON>\n", "axes[0, 2].bar(comparison_df.index, comparison_df['recall'], color=['skyblue', 'lightgreen', 'salmon'])\n", "axes[0, 2].set_title('Rappel')\n", "axes[0, 2].set_ylabel('Rappel')\n", "axes[0, 2].tick_params(axis='x', rotation=45)\n", "for i, v in enumerate(comparison_df['recall']):\n", "    axes[0, 2].text(i, v + 0.01, f'{v:.3f}', ha='center', va='bottom')\n", "\n", "# 4. F1-Score\n", "axes[1, 0].bar(comparison_df.index, comparison_df['f1_score'], color=['skyblue', 'lightgreen', 'salmon'])\n", "axes[1, 0].set_title('F1-Score')\n", "axes[1, 0].set_ylabel('F1-Score')\n", "axes[1, 0].tick_params(axis='x', rotation=45)\n", "for i, v in enumerate(comparison_df['f1_score']):\n", "    axes[1, 0].text(i, v + 0.01, f'{v:.3f}', ha='center', va='bottom')\n", "\n", "# 5. Overfitting\n", "axes[1, 1].bar(comparison_df.index, comparison_df['overfitting'], color=['skyblue', 'lightgreen', 'salmon'])\n", "axes[1, 1].set_title('Overfitting (Train - Test)')\n", "axes[1, 1].set_ylabel('Différence Accuracy')\n", "axes[1, 1].tick_params(axis='x', rotation=45)\n", "axes[1, 1].axhline(y=0, color='red', linestyle='--', alpha=0.7)\n", "for i, v in enumerate(comparison_df['overfitting']):\n", "    axes[1, 1].text(i, v + 0.005, f'{v:.3f}', ha='center', va='bottom')\n", "\n", "# 6. <PERSON><PERSON> d'entraînement\n", "axes[1, 2].bar(comparison_df.index, comparison_df['training_time'], color=['skyblue', 'lightgreen', 'salmon'])\n", "axes[1, 2].set_title('Temps d\\'entraînement')\n", "axes[1, 2].set_ylabel('Temps (secondes)')\n", "axes[1, 2].tick_params(axis='x', rotation=45)\n", "for i, v in enumerate(comparison_df['training_time']):\n", "    axes[1, 2].text(i, v + 0.1, f'{v:.2f}s', ha='center', va='bottom')\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "id": "g1h2i3j4", "metadata": {}, "source": ["### 6. <PERSON><PERSON><PERSON> de confusion comparatives"]}, {"cell_type": "code", "execution_count": null, "id": "k5l6m7n8", "metadata": {}, "outputs": [], "source": ["#souhail ouargui\n", "# Matrices de confusion pour les trois modèles\n", "fig, axes = plt.subplots(1, 3, figsize=(18, 5))\n", "fig.suptitle('Matrices de Confusion - Comparaison des Modèles', fontsize=16, fontweight='bold')\n", "\n", "models = ['Logistique Linéaire', 'Logistique Non Linéaire', 'SVM']\n", "colors = ['Blues', 'Greens', 'Reds']\n", "\n", "for i, (model_name, color) in enumerate(zip(models, colors)):\n", "    cm = confusion_matrix(y_test, predictions[model_name])\n", "    \n", "    sns.heatmap(cm, annot=True, fmt='d', cmap=color, ax=axes[i],\n", "                xticklabels=['Non admis (0)', 'Admis (1)'], \n", "                yticklabels=['Non admis (0)', 'Admis (1)'])\n", "    \n", "    axes[i].set_title(f'{model_name}\\nAccuracy: {results[model_name][\"accuracy_test\"]:.3f}')\n", "    axes[i].set_xlabel('Prédictions')\n", "    if i == 0:\n", "        axes[i].set_ylabel('Vraies valeurs')\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "id": "o9p0q1r2", "metadata": {}, "source": ["### 7. Comparaison des frontières de décision"]}, {"cell_type": "code", "execution_count": null, "id": "s3t4u5v6", "metadata": {}, "outputs": [], "source": ["#souhail ouargui\n", "# Fonction pour tracer les frontières de décision\n", "def plot_decision_boundaries_comparison(X, y, models, scaler):\n", "    fig, axes = plt.subplots(1, 3, figsize=(20, 6))\n", "    fig.suptitle('Comparaison des Frontières de Décision', fontsize=16, fontweight='bold')\n", "    \n", "    # <PERSON><PERSON>er une grille de points\n", "    h = 0.02\n", "    x_min, x_max = X[:, 0].min() - 0.1, X[:, 0].max() + 0.1\n", "    y_min, y_max = X[:, 1].min() - 20, X[:, 1].max() + 20\n", "    xx, yy = np.meshgrid(np.arange(x_min, x_max, h),\n", "                         np.arange(y_min, y_max, h))\n", "    \n", "    grid_points = np.c_[xx.ravel(), yy.ravel()]\n", "    grid_scaled = scaler.transform(grid_points)\n", "    \n", "    model_objects = [lr_linear, lr_nonlinear, svm]\n", "    titles = ['Régression Logistique Linéaire', 'Régression Logistique Non Linéaire', 'SVM']\n", "    \n", "    for i, (model, title) in enumerate(zip(model_objects, titles)):\n", "        # Prédictions sur la grille\n", "        if hasattr(model, 'predict_proba'):\n", "            Z = model.predict_proba(grid_scaled)\n", "        <PERSON><PERSON>(model, 'decision_function'):\n", "            Z = model.decision_function(grid_scaled)\n", "            Z = 1 / (1 + np.exp(-Z))  # Convertir en probabilités\n", "        else:\n", "            Z = model.predict(grid_scaled)\n", "        \n", "        Z = Z.reshape(xx.shape)\n", "        \n", "        # Tracer la frontière de décision\n", "        axes[i].contour(xx, yy, Z, levels=[0.5], colors='orange', linewidths=3)\n", "        axes[i].contourf(xx, yy, Z, levels=50, alpha=0.3, cmap='RdYlBu')\n", "        \n", "        # Tracer les points de données\n", "        colors = ['red' if label == 0 else 'blue' for label in y]\n", "        axes[i].scatter(X[:, 0], X[:, 1], c=colors, alpha=0.7, s=50, edgecolors='black')\n", "        \n", "        axes[i].set_xlabel('GPA')\n", "        if i == 0:\n", "            axes[i].set_ylabel('GRE')\n", "        axes[i].set_title(f'{title}\\nAccuracy: {results[list(results.keys())[i]][\"accuracy_test\"]:.3f}')\n", "        axes[i].grid(True, alpha=0.3)\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "# Afficher la comparaison des frontières\n", "plot_decision_boundaries_comparison(X_test, y_test, [lr_linear, lr_nonlinear, svm], scaler)"]}, {"cell_type": "markdown", "id": "w7x8y9z0", "metadata": {}, "source": ["### 8. <PERSON><PERSON><PERSON> et recommandations"]}, {"cell_type": "code", "execution_count": null, "id": "a1b2c3d4", "metadata": {}, "outputs": [], "source": ["#souhail ouargui\n", "# Analyse d<PERSON>e des résultats\n", "print(\"=\" * 80)\n", "print(\"ANALYSE DÉTAILLÉE DES MODÈLES DE CLASSIFICATION\")\n", "print(\"=\" * 80)\n", "\n", "# Modèle avec la meilleure performance globale\n", "best_model = comparison_df['f1_score'].idxmax()\n", "print(f\"\\n🏆 MODÈLE RECOMMANDÉ: {best_model}\")\n", "print(f\"   Raison: Meilleur équilibre entre précision et rappel (F1-Score: {comparison_df.loc[best_model, 'f1_score']:.3f})\")\n", "\n", "print(f\"\\n📊 ANALYSE PAR MODÈLE:\")\n", "print(f\"-\" * 50)\n", "\n", "# Analyse de chaque modèle\n", "for model_name in comparison_df.index:\n", "    row = comparison_df.loc[model_name]\n", "    print(f\"\\n{model_name.upper()}:\")\n", "    print(f\"  ✅ Points forts:\")\n", "    \n", "    # Identifier les points forts\n", "    if row['accuracy_test'] == comparison_df['accuracy_test'].max():\n", "        print(f\"     - Meilleure accuracy ({row['accuracy_test']:.3f})\")\n", "    if row['precision'] == comparison_df['precision'].max():\n", "        print(f\"     - <PERSON><PERSON><PERSON> précision ({row['precision']:.3f})\")\n", "    if row['recall'] == comparison_df['recall'].max():\n", "        print(f\"     - <PERSON><PERSON><PERSON> rappel ({row['recall']:.3f})\")\n", "    if row['f1_score'] == comparison_df['f1_score'].max():\n", "        print(f\"     - <PERSON><PERSON>ur F1-Score ({row['f1_score']:.3f})\")\n", "    if row['training_time'] == comparison_df['training_time'].min():\n", "        print(f\"     - Plus rapide à entraîner ({row['training_time']:.2f}s)\")\n", "    if row['overfitting'] == comparison_df['overfitting'].min():\n", "        print(f\"     - Moins d'overfitting ({row['overfitting']:.3f})\")\n", "    \n", "    print(f\"  ⚠️  Points faibles:\")\n", "    if row['accuracy_test'] == comparison_df['accuracy_test'].min():\n", "        print(f\"     - Accuracy la plus faible ({row['accuracy_test']:.3f})\")\n", "    if row['training_time'] == comparison_df['training_time'].max():\n", "        print(f\"     - Plus lent à entraîner ({row['training_time']:.2f}s)\")\n", "    if row['overfitting'] == comparison_df['overfitting'].max():\n", "        print(f\"     - Plus d'overfitting ({row['overfitting']:.3f})\")\n", "\n", "print(f\"\\n🎯 RECOMMANDATIONS D'USAGE:\")\n", "print(f\"-\" * 30)\n", "print(f\"• R<PERSON>gression Logistique Linéaire: Idéale pour des relations simples et interprétabilité\")\n", "print(f\"• Régression Logistique Non Linéaire: Meilleure pour capturer des relations complexes\")\n", "print(f\"• SVM: Excellent pour la généralisation et la robustesse aux outliers\")\n", "\n", "print(f\"\\n📈 RÉSUMÉ EXÉCUTIF:\")\n", "print(f\"-\" * 20)\n", "avg_accuracy = comparison_df['accuracy_test'].mean()\n", "std_accuracy = comparison_df['accuracy_test'].std()\n", "print(f\"• Accuracy moyenne: {avg_accuracy:.3f} ± {std_accuracy:.3f}\")\n", "print(f\"• Tous les modèles montrent des performances similaires\")\n", "print(f\"• Le choix dépend du contexte d'application et des contraintes\")\n", "\n", "if std_accuracy < 0.02:\n", "    print(f\"• ✅ Performances très cohérentes entre les modèles\")\n", "else:\n", "    print(f\"• ⚠️  Différences significatives entre les modèles\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.0"}}, "nbformat": 4, "nbformat_minor": 5}