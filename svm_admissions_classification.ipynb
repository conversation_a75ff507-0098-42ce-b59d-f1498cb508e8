{"cells": [{"cell_type": "markdown", "id": "a1b2c3d4", "metadata": {}, "source": ["# Classification SVM - Prédiction d'Admissions\n", "\n", "Implémentation from scratch d'un modèle SVM (Support Vector Machine) pour classer les admis et non admis selon les indicateurs GPA et GRE."]}, {"cell_type": "code", "execution_count": null, "id": "i5j6k7l8", "metadata": {}, "outputs": [], "source": ["#souhail ouargui\n", "import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.preprocessing import StandardScaler\n", "from sklearn.metrics import accuracy_score, confusion_matrix, classification_report\n", "import seaborn as sns\n", "\n", "%matplotlib inline"]}, {"cell_type": "markdown", "id": "e1f2g3h4", "metadata": {}, "source": ["### 1. Dataset"]}, {"cell_type": "code", "execution_count": null, "id": "m9n0o1p2", "metadata": {}, "outputs": [], "source": ["#souhail ouargui\n", "# Chargement du dataset\n", "dataset = pd.read_csv('admissions - admissions.csv')\n", "print(\"Shape du dataset:\", dataset.shape)\n", "print(\"\\nPremières lignes:\")\n", "print(dataset.head())\n", "print(\"\\nDistribution des admissions:\")\n", "print(dataset['admit'].value_counts())\n", "print(\"\\nPourcentage d'admissions:\")\n", "print(dataset['admit'].value_counts(normalize=True) * 100)"]}, {"cell_type": "code", "execution_count": null, "id": "q3r4s5t6", "metadata": {}, "outputs": [], "source": ["#souhail ouargui\n", "# Préparation des données\n", "X = dataset[['gpa', 'gre']].values\n", "y = dataset['admit'].values\n", "\n", "# Convertir les labels en -1 et 1 pour SVM\n", "y = np.where(y == 0, -1, 1)\n", "\n", "print(\"Shape de X:\", X.shape)\n", "print(\"Shape de y:\", y.shape)\n", "print(\"\\nLabels uniques:\", np.unique(y))\n", "print(\"\\nDonnées originales:\")\n", "print(\"GPA - Min:\", X[:, 0].min(), \"Max:\", X[:, 0].max())\n", "print(\"GRE - Min:\", X[:, 1].min(), \"Max:\", X[:, 1].max())"]}, {"cell_type": "code", "execution_count": null, "id": "u7v8w9x0", "metadata": {}, "outputs": [], "source": ["#souhail ouargui\n", "# Séparation train/test AVANT le scaling (bonne pratique)\n", "X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.33, random_state=42)\n", "\n", "print(\"Taille ensemble d'entraînement:\", X_train.shape[0])\n", "print(\"Taille ensemble de test:\", X_test.shape[0])\n", "print(\"\\nDistribution dans l'ensemble d'entraînement:\")\n", "print(\"Non admis (-1):\", np.sum(y_train == -1), \"Admis (1):\", np.sum(y_train == 1))\n", "print(\"\\nDistribution dans l'ensemble de test:\")\n", "print(\"Non admis (-1):\", np.sum(y_test == -1), \"Admis (1):\", np.sum(y_test == 1))"]}, {"cell_type": "code", "execution_count": null, "id": "y1z2a3b4", "metadata": {}, "outputs": [], "source": ["#souhail ouargui\n", "# Normalisation basée uniquement sur X_train (évite le data leakage)\n", "scaler = StandardScaler()\n", "X_train_scaled = scaler.fit_transform(X_train)\n", "X_test_scaled = scaler.transform(X_test)  # Important : on transforme SEULEMENT avec les stats de X_train\n", "\n", "print(\"Données d'entraînement après normalisation:\")\n", "print(\"GPA - Min:\", X_train_scaled[:, 0].min(), \"Max:\", X_train_scaled[:, 0].max())\n", "print(\"GRE - Min:\", X_train_scaled[:, 1].min(), \"Max:\", X_train_scaled[:, 1].max())"]}, {"cell_type": "code", "execution_count": null, "id": "c5d6e7f8", "metadata": {}, "outputs": [], "source": ["#souhail ouargui\n", "# Visualisation des données\n", "plt.figure(figsize=(12, 5))\n", "\n", "plt.subplot(1, 2, 1)\n", "colors = ['red' if label == -1 else 'blue' for label in y_train]\n", "plt.scatter(X_train[:, 0], X_train[:, 1], c=colors, alpha=0.7)\n", "plt.xlabel('GPA')\n", "plt.ylabel('GRE')\n", "plt.title('Données d\\'entraînement (originales)')\n", "plt.legend(['Non admis (-1)', 'Admis (1)'])\n", "\n", "plt.subplot(1, 2, 2)\n", "colors = ['red' if label == -1 else 'blue' for label in y_train]\n", "plt.scatter(X_train_scaled[:, 0], X_train_scaled[:, 1], c=colors, alpha=0.7)\n", "plt.xlabel('GPA (normalisé)')\n", "plt.ylabel('GRE (normalisé)')\n", "plt.title('Données d\\'entraînement (normalisées)')\n", "plt.legend(['Non admis (-1)', 'Admis (1)'])\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "id": "k3l4m5n6", "metadata": {}, "source": ["### 2. Implémentation SVM from scratch"]}, {"cell_type": "code", "execution_count": null, "id": "o7p8q9r0", "metadata": {}, "outputs": [], "source": ["#souhail ouargui\n", "class SVM:\n", "    def __init__(self, learning_rate=0.001, lambda_param=0.01, n_iters=1000):\n", "        self.lr = learning_rate\n", "        self.lambda_param = lambda_param\n", "        self.n_iters = n_iters\n", "        self.w = None\n", "        self.b = None\n", "        self.cost_history = []\n", "    \n", "    def fit(self, X, y):\n", "        n_samples, n_features = X.shape\n", "        \n", "        # Initialisation des paramètres\n", "        self.w = np.zeros(n_features)\n", "        self.b = 0\n", "        \n", "        # Gradient descent\n", "        for i in range(self.n_iters):\n", "            # Calcul des coûts et gradients\n", "            cost = 0\n", "            dw = np.zeros(n_features)\n", "            db = 0\n", "            \n", "            for idx, x_i in enumerate(X):\n", "                condition = y[idx] * (np.dot(x_i, self.w) - self.b) >= 1\n", "                \n", "                if condition:\n", "                    # Point correctement classé\n", "                    cost += 0\n", "                    dw += self.lambda_param * self.w\n", "                    db += 0\n", "                else:\n", "                    # Point mal classé ou dans la marge\n", "                    cost += 1 - y[idx] * (np.dot(x_i, self.w) - self.b)\n", "                    dw += self.lambda_param * self.w - y[idx] * x_i\n", "                    db += y[idx]\n", "            \n", "            # Coût total avec régularisation\n", "            total_cost = self.lambda_param * np.dot(self.w, self.w) + cost / n_samples\n", "            self.cost_history.append(total_cost)\n", "            \n", "            # Mise à jour des paramètres\n", "            self.w -= self.lr * dw / n_samples\n", "            self.b -= self.lr * db / n_samples\n", "            \n", "            # Affichage du coût toutes les 200 itérations\n", "            if i % 200 == 0:\n", "                print(f\"Coût après {i} itérations: {total_cost:.6f}\")\n", "    \n", "    def predict(self, X):\n", "        approximation = np.dot(X, self.w) - self.b\n", "        return np.sign(approximation)\n", "    \n", "    def decision_function(self, X):\n", "        return np.dot(X, self.w) - self.b"]}, {"cell_type": "markdown", "id": "s1t2u3v4", "metadata": {}, "source": ["### 3. Entraînement du modèle SVM"]}, {"cell_type": "code", "execution_count": null, "id": "w5x6y7z8", "metadata": {}, "outputs": [], "source": ["#souhail ouargui\n", "# Création et entraînement du modèle SVM\n", "print(\"Entraînement du modèle SVM...\")\n", "svm = SVM(learning_rate=0.001, lambda_param=0.01, n_iters=1000)\n", "svm.fit(X_train_scaled, y_train)\n", "\n", "print(f\"\\nParamètres finaux:\")\n", "print(f\"Poids (w): {svm.w}\")\n", "print(f\"Biais (b): {svm.b:.6f}\")"]}, {"cell_type": "code", "execution_count": null, "id": "a9b0c1d2", "metadata": {}, "outputs": [], "source": ["#souhail ouargui\n", "# Visualisation de l'évolution du coût\n", "plt.figure(figsize=(10, 6))\n", "plt.plot(svm.cost_history)\n", "plt.xlabel('Nombre d\\'itérations')\n", "plt.ylabel('Coût SVM')\n", "plt.title('Évolution de la fonction de coût SVM')\n", "plt.grid(True)\n", "plt.show()"]}, {"cell_type": "markdown", "id": "e3f4g5h6", "metadata": {}, "source": ["### 4. Évaluation du modèle"]}, {"cell_type": "code", "execution_count": null, "id": "i7j8k9l0", "metadata": {}, "outputs": [], "source": ["#souhail ouargui\n", "# Prédictions sur l'ensemble d'entraînement\n", "y_pred_train = svm.predict(X_train_scaled)\n", "accuracy_train = accuracy_score(y_train, y_pred_train)\n", "print(f\"Accuracy sur l'ensemble d'entraînement: {accuracy_train:.3f}\")\n", "\n", "# Prédictions sur l'ensemble de test\n", "y_pred_test = svm.predict(X_test_scaled)\n", "accuracy_test = accuracy_score(y_test, y_pred_test)\n", "print(f\"Accuracy sur l'ensemble de test: {accuracy_test:.3f}\")\n", "\n", "# Rapport de classification détaillé\n", "print(\"\\nRapport de classification sur l'ensemble de test:\")\n", "print(classification_report(y_test, y_pred_test, target_names=['Non admis (-1)', 'Admis (1)']))"]}, {"cell_type": "code", "execution_count": null, "id": "m1n2o3p4", "metadata": {}, "outputs": [], "source": ["#souhail ouargui\n", "# <PERSON><PERSON> de confusion\n", "cm = confusion_matrix(y_test, y_pred_test)\n", "print(\"\\nMatrice de confusion:\")\n", "print(cm)\n", "\n", "# Visualisation de la matrice de confusion\n", "plt.figure(figsize=(8, 6))\n", "sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', \n", "            xticklabels=['Non admis (-1)', 'Admis (1)'], \n", "            yticklabels=['Non admis (-1)', 'Admis (1)'])\n", "plt.xlabel('Prédictions')\n", "plt.ylabel('Vraies valeurs')\n", "plt.title('<PERSON><PERSON> de confusion - SVM')\n", "plt.show()"]}, {"cell_type": "markdown", "id": "q5r6s7t8", "metadata": {}, "source": ["### 5. Visualisation de la frontière de décision"]}, {"cell_type": "code", "execution_count": null, "id": "u9v0w1x2", "metadata": {}, "outputs": [], "source": ["#souhail ouargui\n", "def plot_svm_decision_boundary(X, y, svm_model, scaler, title=\"Frontière de décision SVM\"):\n", "    plt.figure(figsize=(12, 8))\n", "    \n", "    # C<PERSON>er une grille de points dans l'espace original\n", "    h = 0.02\n", "    x_min, x_max = X[:, 0].min() - 0.1, X[:, 0].max() + 0.1\n", "    y_min, y_max = X[:, 1].min() - 20, X[:, 1].max() + 20\n", "    xx, yy = np.meshgrid(np.arange(x_min, x_max, h),\n", "                         np.arange(y_min, y_max, h))\n", "    \n", "    # Transformer la grille\n", "    grid_points = np.c_[xx.ravel(), yy.ravel()]\n", "    grid_scaled = scaler.transform(grid_points)\n", "    \n", "    # Prédire sur la grille\n", "    Z = svm_model.decision_function(grid_scaled)\n", "    Z = Z.reshape(xx.shape)\n", "    \n", "    # Tracer la frontière de décision et les marges\n", "    plt.contour(xx, yy, Z, levels=[-1, 0, 1], colors=['red', 'black', 'blue'], \n", "               linestyles=['--', '-', '--'], linewidths=[2, 3, 2])\n", "    plt.contourf(xx, yy, Z, levels=50, alpha=0.3, cmap='RdYlBu')\n", "    \n", "    # Tracer les points de données\n", "    colors = ['red' if label == -1 else 'blue' for label in y]\n", "    scatter = plt.scatter(X[:, 0], X[:, 1], c=colors, alpha=0.7, s=50, edgecolors='black')\n", "    \n", "    plt.xlabel('GPA')\n", "    plt.ylabel('GRE')\n", "    plt.title(title)\n", "    plt.legend(['<PERSON><PERSON> (-1)', '<PERSON><PERSON> (0)', '<PERSON><PERSON> (+1)', 'Non admis (-1)', 'Admis (1)'])\n", "    plt.grid(True, alpha=0.3)\n", "    plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "y3z4a5b6", "metadata": {}, "outputs": [], "source": ["#souhail ouargui\n", "# Visualisation sur l'ensemble d'entraînement\n", "plot_svm_decision_boundary(X_train, y_train, svm, scaler, \n", "                          \"Frontière de décision SVM - Ensemble d'entraînement\")"]}, {"cell_type": "code", "execution_count": null, "id": "c7d8e9f0", "metadata": {}, "outputs": [], "source": ["#souhail ouargui\n", "# Visualisation sur l'ensemble de test\n", "plot_svm_decision_boundary(X_test, y_test, svm, scaler, \n", "                          \"Frontière de décision SVM - Ensemble de test\")"]}, {"cell_type": "markdown", "id": "g1h2i3j4", "metadata": {}, "source": ["### 6. Pré<PERSON> sur de nouveaux exemples"]}, {"cell_type": "code", "execution_count": null, "id": "k5l6m7n8", "metadata": {}, "outputs": [], "source": ["#souhail ouargui\n", "# Exemples de nouveaux étudiants (GPA, GRE)\n", "nouveaux_etudiants = np.array([\n", "    [3.5, 650],  # <PERSON>\n", "    [2.8, 500],  # <PERSON><PERSON><PERSON><PERSON> moyen\n", "    [3.9, 750],  # Excellent étudiant\n", "    [2.5, 450],  # <PERSON><PERSON><PERSON><PERSON> faible\n", "    [3.2, 600],  # Étudiant correct\n", "])\n", "\n", "# Normaliser avec le même scaler\n", "nouveaux_etudiants_scaled = scaler.transform(nouveaux_etudiants)\n", "\n", "# Prédictions\n", "predictions = svm.predict(nouveaux_etudiants_scaled)\n", "scores = svm.decision_function(nouveaux_etudiants_scaled)\n", "\n", "print(\"Prédictions pour de nouveaux étudiants (SVM):\")\n", "print(\"GPA\\tGRE\\tScore\\t\\tPrédiction\")\n", "print(\"-\" * 50)\n", "for i, (etudiant, score, pred) in enumerate(zip(nouveaux_etudiants, scores, predictions)):\n", "    gpa, gre = etudiant\n", "    admission = \"Admis (+1)\" if pred == 1 else \"Non admis (-1)\"\n", "    print(f\"{gpa:.1f}\\t{gre:.0f}\\t{score:.3f}\\t\\t{admission}\")"]}, {"cell_type": "code", "execution_count": null, "id": "o9p0q1r2", "metadata": {}, "outputs": [], "source": ["#souhail ouargui\n", "# Visualisation des nouveaux exemples sur la frontière de décision\n", "plt.figure(figsize=(12, 8))\n", "\n", "# Tracer la frontière de décision avec les données de test\n", "h = 0.02\n", "x_min, x_max = X_test[:, 0].min() - 0.1, X_test[:, 0].max() + 0.1\n", "y_min, y_max = X_test[:, 1].min() - 20, X_test[:, 1].max() + 20\n", "xx, yy = np.meshgrid(np.arange(x_min, x_max, h),\n", "                     np.arange(y_min, y_max, h))\n", "\n", "grid_points = np.c_[xx.ravel(), yy.ravel()]\n", "grid_scaled = scaler.transform(grid_points)\n", "Z = svm.decision_function(grid_scaled)\n", "Z = Z.reshape(xx.shape)\n", "\n", "plt.contour(xx, yy, Z, levels=[-1, 0, 1], colors=['red', 'black', 'blue'], \n", "           linestyles=['--', '-', '--'], linewidths=[2, 3, 2])\n", "plt.contourf(xx, yy, Z, levels=50, alpha=0.3, cmap='RdYlBu')\n", "\n", "# Tracer les données de test\n", "colors_test = ['red' if label == -1 else 'blue' for label in y_test]\n", "plt.scatter(X_test[:, 0], X_test[:, 1], c=colors_test, alpha=0.6, s=50, \n", "           label='Données de test', edgecolors='black')\n", "\n", "# Tracer les nouveaux exemples\n", "plt.scatter(nouveaux_etudiants[:, 0], nouveaux_etudiants[:, 1], \n", "           c='yellow', s=200, marker='*', edgecolors='black', \n", "           label='Nouveaux étudiants')\n", "\n", "plt.xlabel('GPA')\n", "plt.ylabel('GRE')\n", "plt.title('Prédictions sur de nouveaux exemples - SVM')\n", "plt.legend()\n", "plt.grid(True, alpha=0.3)\n", "plt.show()"]}, {"cell_type": "markdown", "id": "s3t4u5v6", "metadata": {}, "source": ["### 7. Comparaison avec scikit-learn"]}, {"cell_type": "code", "execution_count": null, "id": "w7x8y9z0", "metadata": {}, "outputs": [], "source": ["#souhail ouargui\n", "# Comparaison avec SVM de scikit-learn\n", "from sklearn.svm import SVC\n", "\n", "# SVM linéaire avec scikit-learn\n", "sklearn_svm = SVC(kernel='linear', C=1.0, random_state=42)\n", "sklearn_svm.fit(X_train_scaled, y_train)\n", "y_pred_sklearn = sklearn_svm.predict(X_test_scaled)\n", "accuracy_sklearn = accuracy_score(y_test, y_pred_sklearn)\n", "\n", "print(\"Comparaison des modèles SVM:\")\n", "print(\"=\" * 50)\n", "print(f\"Notre implémentation SVM (from scratch):\")\n", "print(f\"  - Accuracy: {accuracy_test:.3f}\")\n", "print(f\"  - Poids: {svm.w}\")\n", "print(f\"  - Biais: {svm.b:.6f}\")\n", "\n", "print(f\"\\nScikit-learn SVM:\")\n", "print(f\"  - Accuracy: {accuracy_sklearn:.3f}\")\n", "print(f\"  - Poids: {sklearn_svm.coef_[0]}\")\n", "print(f\"  - Biais: {sklearn_svm.intercept_[0]:.6f}\")\n", "\n", "print(f\"\\nDifférence d'accuracy: {abs(accuracy_test - accuracy_sklearn):.3f}\")"]}, {"cell_type": "markdown", "id": "a1b2c3d4", "metadata": {}, "source": ["### 8. <PERSON><PERSON><PERSON> des résultats et conclusion"]}, {"cell_type": "code", "execution_count": null, "id": "e5f6g7h8", "metadata": {}, "outputs": [], "source": ["#souhail ouargui\n", "# Analyse des résultats SVM\n", "print(\"=\" * 60)\n", "print(\"ANALYSE DU MODÈLE SVM POUR LA CLASSIFICATION D'ADMISSIONS\")\n", "print(\"=\" * 60)\n", "\n", "print(f\"\\nÉquation de l'hyperplan de séparation:\")\n", "print(f\"{svm.w[0]:.4f} * GPA + {svm.w[1]:.4f} * GRE - {svm.b:.4f} = 0\")\n", "\n", "print(f\"\\nInterprétation des coefficients:\")\n", "if svm.w[0] > 0:\n", "    print(f\"  - GPA: +{svm.w[0]:.4f} (impact positif sur l'admission)\")\n", "else:\n", "    print(f\"  - GPA: {svm.w[0]:.4f} (impact négatif sur l'admission)\")\n", "    \n", "if svm.w[1] > 0:\n", "    print(f\"  - GRE: +{svm.w[1]:.4f} (impact positif sur l'admission)\")\n", "else:\n", "    print(f\"  - GRE: {svm.w[1]:.4f} (impact négatif sur l'admission)\")\n", "\n", "print(f\"\\nCaractéristiques du modèle SVM:\")\n", "print(f\"  - Frontière de décision: LINÉAIRE (hyperplan)\")\n", "print(f\"  - Marges: Maximisées pour une meilleure généralisation\")\n", "print(f\"  - <PERSON><PERSON><PERSON><PERSON>: Moins sensible aux outliers que d'autres méthodes\")\n", "\n", "print(f\"\\nPerformances finales:\")\n", "print(f\"  - Accuracy d'entraînement: {accuracy_train:.1%}\")\n", "print(f\"  - Accuracy de test: {accuracy_test:.1%}\")\n", "print(f\"  - Différence (overfitting): {accuracy_train - accuracy_test:.1%}\")\n", "\n", "if accuracy_train - accuracy_test > 0.05:\n", "    print(f\"  ⚠️  Possible overfitting détecté\")\n", "else:\n", "    print(f\"  ✅ Pas d'overfitting significatif\")\n", "\n", "print(f\"\\nAvantages du SVM:\")\n", "print(f\"  - <PERSON><PERSON> la marge entre les classes\")\n", "print(f\"  - Efficace en haute dimension\")\n", "print(f\"  - Utilise seulement les vecteurs de support\")\n", "print(f\"  - Peut utiliser différents noyaux (kernel trick)\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.0"}}, "nbformat": 4, "nbformat_minor": 5}