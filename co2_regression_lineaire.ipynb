{"cells": [{"cell_type": "markdown", "id": "1", "metadata": {}, "source": ["#souhail ouargui\n", "# <PERSON><PERSON>gression Linéaire Multiple - Prédiction du CO2\n", "## Dataset: occupancy.csv\n", "### Variables: Temperature, Humidity, Light, HumidityRatio → CO2"]}, {"cell_type": "code", "execution_count": null, "id": "2", "metadata": {}, "outputs": [], "source": ["#souhail ouargui\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.preprocessing import StandardScaler\n", "from sklearn.metrics import mean_squared_error, r2_score\n", "%matplotlib inline"]}, {"cell_type": "markdown", "id": "3", "metadata": {}, "source": ["## 1. Chargement et exploration du dataset"]}, {"cell_type": "code", "execution_count": null, "id": "4", "metadata": {}, "outputs": [], "source": ["#souhail ouargui\n", "# Chargement du dataset\n", "dataset = pd.read_csv('occupancy.csv')\n", "print(\"Shape du dataset:\", dataset.shape)\n", "print(\"\\nPremières lignes:\")\n", "dataset.head()"]}, {"cell_type": "code", "execution_count": null, "id": "5", "metadata": {}, "outputs": [], "source": ["#souhail ouargui\n", "# Informations sur le dataset\n", "print(\"Informations sur le dataset:\")\n", "dataset.info()\n", "print(\"\\nStatistiques descriptives:\")\n", "dataset.describe()"]}, {"cell_type": "code", "execution_count": null, "id": "6", "metadata": {}, "outputs": [], "source": ["#souhail ouargui\n", "# Sélection des variables pour la régression\n", "# Variables indépendantes: Temperature, Humidity, Light, HumidityRatio\n", "# Variable dépendante: CO2\n", "features = ['Temperature', 'Humidity', 'Light', 'HumidityRatio']\n", "target = 'CO2'\n", "\n", "X = dataset[features].values\n", "y = dataset[target].values.reshape(-1, 1)\n", "\n", "print(f\"Shape de X: {X.shape}\")\n", "print(f\"Shape de y: {y.shape}\")\n", "print(f\"\\nVariables utilisées: {features}\")\n", "print(f\"Variable cible: {target}\")"]}, {"cell_type": "markdown", "id": "7", "metadata": {}, "source": ["## 2. Visualisation des données"]}, {"cell_type": "code", "execution_count": null, "id": "8", "metadata": {}, "outputs": [], "source": ["#souhail ouargui\n", "# Visualisation des relations entre variables\n", "fig, axes = plt.subplots(2, 2, figsize=(15, 10))\n", "fig.suptitle('Relations entre les variables et CO2', fontsize=16)\n", "\n", "for i, feature in enumerate(features):\n", "    row = i // 2\n", "    col = i % 2\n", "    axes[row, col].scatter(dataset[feature], dataset[target], alpha=0.5)\n", "    axes[row, col].set_xlabel(feature)\n", "    axes[row, col].set_ylabel('CO2')\n", "    axes[row, col].set_title(f'{feature} vs CO2')\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "id": "9", "metadata": {}, "source": ["## 3. Préparation des données"]}, {"cell_type": "code", "execution_count": null, "id": "10", "metadata": {}, "outputs": [], "source": ["#souhail ouargui\n", "# Division train/test (80/20)\n", "X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)\n", "\n", "print(f\"Taille du set d'entraînement: {X_train.shape[0]} échantillons\")\n", "print(f\"Taille du set de test: {X_test.shape[0]} échantillons\")\n", "print(f\"Shape X_train: {X_train.shape}\")\n", "print(f\"Shape y_train: {y_train.shape}\")"]}, {"cell_type": "code", "execution_count": null, "id": "11", "metadata": {}, "outputs": [], "source": ["#souhail ouargui\n", "# Normalisation des données (StandardScaler)\n", "# Important: fit sur train seulement, puis transform sur train et test\n", "scaler = StandardScaler()\n", "X_train_scaled = scaler.fit_transform(X_train)\n", "X_test_scaled = scaler.transform(X_test)\n", "\n", "print(\"Moyennes avant normalisation (train):\")\n", "print(np.mean(X_train, axis=0))\n", "print(\"\\nMoyennes après normalisation (train):\")\n", "print(np.mean(X_train_scaled, axis=0))\n", "print(\"\\nÉcarts-types après normalisation (train):\")\n", "print(np.std(X_train_scaled, axis=0))"]}, {"cell_type": "markdown", "id": "12", "metadata": {}, "source": ["## 4. Implémentation du modèle de régression linéaire multiple (from scratch)\n", "### Suivant la méthode du professeur"]}, {"cell_type": "code", "execution_count": null, "id": "13", "metadata": {}, "outputs": [], "source": ["#souhail ouargui\n", "# Ajout de la colonne de biais (intercept) à la matrice X\n", "# Création de la matrice X avec colonne de 1 pour le biais\n", "def add_intercept(X):\n", "    \"\"\"\n", "    Ajoute une colonne de 1 au début de la matrice X pour le terme de biais\n", "    \"\"\"\n", "    intercept = np.ones((X.shape[0], 1))\n", "    return np.hstack((intercept, X))\n", "\n", "# Application aux données d'entraînement et de test\n", "X_train_with_intercept = add_intercept(X_train_scaled)\n", "X_test_with_intercept = add_intercept(X_test_scaled)\n", "\n", "print(f\"Shape X_train avec intercept: {X_train_with_intercept.shape}\")\n", "print(f\"Shape X_test avec intercept: {X_test_with_intercept.shape}\")\n", "print(f\"\\nPremières lignes de X_train avec intercept:\")\n", "print(X_train_with_intercept[:3])"]}, {"cell_type": "code", "execution_count": null, "id": "14", "metadata": {}, "outputs": [], "source": ["#souhail ouargui\n", "# Fonction de prédiction (modèle linéaire)\n", "def model(X, theta):\n", "    \"\"\"\n", "    Calcule les prédictions du modèle linéaire\n", "    X: matrice des features (avec intercept)\n", "    theta: vecteur des paramètres\n", "    \"\"\"\n", "    return X.dot(theta)\n", "\n", "# Fonction de coût (Mean Squared Error)\n", "def cost_function(X, y, theta):\n", "    \"\"\"\n", "    Calcule la fonction de coût (MSE)\n", "    \"\"\"\n", "    m = len(y)\n", "    predictions = model(X, theta)\n", "    cost = (1/(2*m)) * np.sum((predictions - y)**2)\n", "    return cost\n", "\n", "# Fonction pour calculer les gradients\n", "def gradients(X, y, theta):\n", "    \"\"\"\n", "    Calcule les gradients de la fonction de coût\n", "    \"\"\"\n", "    m = len(y)\n", "    predictions = model(X, theta)\n", "    gradients = (1/m) * X.T.dot(predictions - y)\n", "    return gradients"]}, {"cell_type": "code", "execution_count": null, "id": "15", "metadata": {}, "outputs": [], "source": ["#souhail ouargui\n", "# Algorithme de gradient descent\n", "def gradient_descent(X, y, theta, learning_rate, n_iterations):\n", "    \"\"\"\n", "    Implémente l'algorithme de gradient descent\n", "    \"\"\"\n", "    cost_history = []\n", "    \n", "    for i in range(n_iterations):\n", "        # Calcul du coût actuel\n", "        cost = cost_function(X, y, theta)\n", "        cost_history.append(cost)\n", "        \n", "        # Calcul des gradients\n", "        grads = gradients(X, y, theta)\n", "        \n", "        # Mise à jour des paramètres\n", "        theta = theta - learning_rate * grads\n", "        \n", "        # Affichage du progrès\n", "        if i % 1000 == 0:\n", "            print(f\"Itération {i}: Coût = {cost:.4f}\")\n", "    \n", "    return theta, cost_history\n", "\n", "# Initialisation des paramètres\n", "n_features = X_train_with_intercept.shape[1]  # 5 features (4 + intercept)\n", "theta_initial = np.random.normal(0, 0.01, (n_features, 1))\n", "\n", "print(f\"Nombre de paramètres: {n_features}\")\n", "print(f\"Shape de theta initial: {theta_initial.shape}\")\n", "print(f\"Theta initial: {theta_initial.flatten()}\")"]}, {"cell_type": "markdown", "id": "16", "metadata": {}, "source": ["## 5. Entraînement du modèle"]}, {"cell_type": "code", "execution_count": null, "id": "17", "metadata": {}, "outputs": [], "source": ["#souhail ouargui\n", "# Paramètres d'entraînement\n", "learning_rate = 0.01\n", "n_iterations = 10000\n", "\n", "print(f\"Paramètres d'entraînement:\")\n", "print(f\"Learning rate: {learning_rate}\")\n", "print(f\"Nombre d'itérations: {n_iterations}\")\n", "print(f\"\\nDébut de l'entraînement...\")\n", "\n", "# Entraînement du modèle\n", "theta_final, cost_history = gradient_descent(\n", "    X_train_with_intercept, \n", "    y_train, \n", "    theta_initial, \n", "    learning_rate, \n", "    n_iterations\n", ")\n", "\n", "print(f\"\\nEntraînement terminé!\")\n", "print(f\"Coût final: {cost_history[-1]:.4f}\")\n", "print(f\"\\nParamètres finaux (theta):\")\n", "print(f\"Intercept (θ0): {theta_final[0][0]:.4f}\")\n", "for i, feature in enumerate(features):\n", "    print(f\"{feature} (θ{i+1}): {theta_final[i+1][0]:.4f}\")"]}, {"cell_type": "markdown", "id": "18", "metadata": {}, "source": ["## 6. Visualisation de la convergence"]}, {"cell_type": "code", "execution_count": null, "id": "19", "metadata": {}, "outputs": [], "source": ["#souhail ouargui\n", "# Graphique de convergence de la fonction de coût\n", "plt.figure(figsize=(12, 5))\n", "\n", "# Graphique 1: Évolution complète du coût\n", "plt.subplot(1, 2, 1)\n", "plt.plot(cost_history)\n", "plt.title('Évolution de la fonction de coût')\n", "plt.xlabel('Itérations')\n", "plt.ylabel('<PERSON><PERSON><PERSON> (MSE)')\n", "plt.grid(True)\n", "\n", "# Graphique 2: Zoom sur les dernières itérations\n", "plt.subplot(1, 2, 2)\n", "plt.plot(cost_history[1000:])\n", "plt.title('Convergence (dernières itérations)')\n", "plt.xlabel('Itérations (à partir de 1000)')\n", "plt.ylabel('<PERSON><PERSON><PERSON> (MSE)')\n", "plt.grid(True)\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(f\"Coût initial: {cost_history[0]:.4f}\")\n", "print(f\"Coût final: {cost_history[-1]:.4f}\")\n", "print(f\"Réduction du coût: {((cost_history[0] - cost_history[-1]) / cost_history[0] * 100):.2f}%\")"]}, {"cell_type": "markdown", "id": "20", "metadata": {}, "source": ["## 7. Évaluation du modèle"]}, {"cell_type": "code", "execution_count": null, "id": "21", "metadata": {}, "outputs": [], "source": ["#souhail ouargui\n", "# Prédictions sur les ensembles d'entraînement et de test\n", "y_train_pred = model(X_train_with_intercept, theta_final)\n", "y_test_pred = model(X_test_with_intercept, theta_final)\n", "\n", "# Calcul des métriques\n", "# MSE (Mean Squared Error)\n", "mse_train = mean_squared_error(y_train, y_train_pred)\n", "mse_test = mean_squared_error(y_test, y_test_pred)\n", "\n", "# R² (Coefficient de détermination)\n", "r2_train = r2_score(y_train, y_train_pred)\n", "r2_test = r2_score(y_test, y_test_pred)\n", "\n", "# RMSE (Root Mean Squared Error)\n", "rmse_train = np.sqrt(mse_train)\n", "rmse_test = np.sqrt(mse_test)\n", "\n", "print(\"=== MÉTRIQUES D'ÉVALUATION ===\")\n", "print(f\"\\nEnsemble d'entraînement:\")\n", "print(f\"  MSE:  {mse_train:.4f}\")\n", "print(f\"  RMSE: {rmse_train:.4f}\")\n", "print(f\"  R²:   {r2_train:.4f}\")\n", "\n", "print(f\"\\nEnsemble de test:\")\n", "print(f\"  MSE:  {mse_test:.4f}\")\n", "print(f\"  RMSE: {rmse_test:.4f}\")\n", "print(f\"  R²:   {r2_test:.4f}\")\n", "\n", "print(f\"\\nDifférence train/test:\")\n", "print(f\"  RMSE: {abs(rmse_train - rmse_test):.4f}\")\n", "print(f\"  R²:   {abs(r2_train - r2_test):.4f}\")"]}, {"cell_type": "code", "execution_count": null, "id": "22", "metadata": {}, "outputs": [], "source": ["#souhail ouargui\n", "# Visualisation des prédictions vs valeurs réelles\n", "plt.figure(figsize=(15, 5))\n", "\n", "# Graphique 1: Ensemble d'entraînement\n", "plt.subplot(1, 3, 1)\n", "plt.scatter(y_train, y_train_pred, alpha=0.5)\n", "plt.plot([y_train.min(), y_train.max()], [y_train.min(), y_train.max()], 'r--', lw=2)\n", "plt.xlabel('Valeurs réelles')\n", "plt.ylabel('Prédictions')\n", "plt.title(f'Train Set (R² = {r2_train:.3f})')\n", "plt.grid(True)\n", "\n", "# Graphique 2: Ensemble de test\n", "plt.subplot(1, 3, 2)\n", "plt.scatter(y_test, y_test_pred, alpha=0.5, color='orange')\n", "plt.plot([y_test.min(), y_test.max()], [y_test.min(), y_test.max()], 'r--', lw=2)\n", "plt.xlabel('Valeurs réelles')\n", "plt.ylabel('Prédictions')\n", "plt.title(f'Test Set (R² = {r2_test:.3f})')\n", "plt.grid(True)\n", "\n", "# Graphique 3: <PERSON><PERSON><PERSON><PERSON>\n", "plt.subplot(1, 3, 3)\n", "residuals_test = y_test - y_test_pred\n", "plt.scatter(y_test_pred, residuals_test, alpha=0.5, color='green')\n", "plt.axhline(y=0, color='r', linestyle='--')\n", "plt.xlabel('Prédictions')\n", "plt.ylabel('<PERSON><PERSON><PERSON><PERSON>')\n", "plt.title('<PERSON><PERSON><PERSON> rés<PERSON> (Test)')\n", "plt.grid(True)\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "id": "23", "metadata": {}, "source": ["## 8. <PERSON><PERSON><PERSON> du modèle final"]}, {"cell_type": "code", "execution_count": null, "id": "24", "metadata": {}, "outputs": [], "source": ["#souhail ouargui\n", "# Affichage de l'équation du modèle\n", "print(\"=== ÉQUATION DU MODÈLE DE RÉGRESSION LINÉAIRE MULTIPLE ===\")\n", "print(\"\\nCO2 = θ0 + θ1*Temperature + θ2*Humidity + θ3*Light + θ4*HumidityRatio\")\n", "print(\"\\nAvec les paramètres optimisés:\")\n", "print(f\"CO2 = {theta_final[0][0]:.4f} + {theta_final[1][0]:.4f}*Temperature + {theta_final[2][0]:.4f}*Humidity + {theta_final[3][0]:.4f}*Light + {theta_final[4][0]:.4f}*HumidityRatio\")\n", "\n", "print(\"\\n=== INTERPRÉTATION DES COEFFICIENTS ===\")\n", "print(f\"θ0 (Intercept): {theta_final[0][0]:.4f} - Valeur de base du CO2\")\n", "for i, feature in enumerate(features):\n", "    coef = theta_final[i+1][0]\n", "    if coef > 0:\n", "        effect = \"augmente\"\n", "    else:\n", "        effect = \"diminue\"\n", "    print(f\"θ{i+1} ({feature}): {coef:.4f} - Une augmentation de 1 unité de {feature} {effect} le CO2 de {abs(coef):.4f}\")"]}, {"cell_type": "markdown", "id": "25", "metadata": {}, "source": ["## 9. Test de prédiction sur de nouveaux échantillons"]}, {"cell_type": "code", "execution_count": null, "id": "26", "metadata": {}, "outputs": [], "source": ["#souhail ouargui\n", "# Fonction pour faire des prédictions sur de nouvelles données\n", "def predict_co2(temperature, humidity, light, humidity_ratio, theta, scaler):\n", "    \"\"\"\n", "    Prédit le CO2 pour de nouvelles valeurs\n", "    \"\"\"\n", "    # C<PERSON>er un array avec les nouvelles valeurs\n", "    new_data = np.array([[temperature, humidity, light, humidity_ratio]])\n", "    \n", "    # Normaliser avec le même scaler que l'entraînement\n", "    new_data_scaled = scaler.transform(new_data)\n", "    \n", "    # Ajouter l'intercept\n", "    new_data_with_intercept = add_intercept(new_data_scaled)\n", "    \n", "    # Faire la prédiction\n", "    prediction = model(new_data_with_intercept, theta)\n", "    \n", "    return prediction[0][0]\n", "\n", "# Exemples de prédictions\n", "print(\"=== EXEMPLES DE PRÉDICTIONS ===\")\n", "print(\"\\nTest avec quelques valeurs du dataset:\")\n", "\n", "# Prendre quelques échantillons du dataset original\n", "for i in range(3):\n", "    temp = dataset.iloc[i]['Temperature']\n", "    hum = dataset.iloc[i]['Humidity']\n", "    light = dataset.iloc[i]['Light']\n", "    hum_ratio = dataset.iloc[i]['HumidityRatio']\n", "    real_co2 = dataset.iloc[i]['CO2']\n", "    \n", "    predicted_co2 = predict_co2(temp, hum, light, hum_ratio, theta_final, scaler)\n", "    \n", "    print(f\"\\nÉchantillon {i+1}:\")\n", "    print(f\"  Temperature: {temp:.2f}, Humidity: {hum:.2f}, Light: {light:.2f}, HumidityRatio: {hum_ratio:.6f}\")\n", "    print(f\"  CO2 réel: {real_co2:.2f}\")\n", "    print(f\"  CO2 prédit: {predicted_co2:.2f}\")\n", "    print(f\"  Erreur: {abs(real_co2 - predicted_co2):.2f}\")"]}, {"cell_type": "markdown", "id": "27", "metadata": {}, "source": ["## 10. <PERSON><PERSON><PERSON><PERSON> et conclusions"]}, {"cell_type": "code", "execution_count": null, "id": "28", "metadata": {}, "outputs": [], "source": ["#souhail ouargui\n", "print(\"=== R<PERSON>SUMÉ DU MODÈLE DE RÉGRESSION LINÉAIRE MULTIPLE ===\")\n", "print(f\"\\nDataset: occupancy.csv ({dataset.shape[0]} échantillons)\")\n", "print(f\"Variables prédictives: {', '.join(features)}\")\n", "print(f\"Variable cible: {target}\")\n", "\n", "print(f\"\\nParamètres d'entraînement:\")\n", "print(f\"  - Learning rate: {learning_rate}\")\n", "print(f\"  - Nombre d'itérations: {n_iterations}\")\n", "print(f\"  - Algorithme: Gradient Descent\")\n", "\n", "print(f\"\\nPerformances du modèle:\")\n", "print(f\"  - R² (test): {r2_test:.4f} ({r2_test*100:.2f}% de variance expliquée)\")\n", "print(f\"  - RMSE (test): {rmse_test:.4f}\")\n", "print(f\"  - MSE (test): {mse_test:.4f}\")\n", "\n", "print(f\"\\nConclusions:\")\n", "if r2_test > 0.8:\n", "    print(f\"  ✓ Excellent modèle (R² > 0.8)\")\n", "elif r2_test > 0.6:\n", "    print(f\"  ✓ Bon modèle (R² > 0.6)\")\n", "elif r2_test > 0.4:\n", "    print(f\"  ⚠ Modèle acceptable (R² > 0.4)\")\n", "else:\n", "    print(f\"  ✗ Modèle peu performant (R² < 0.4)\")\n", "\n", "print(f\"\\n  - Le modèle explique {r2_test*100:.1f}% de la variabilité du CO2\")\n", "print(f\"  - <PERSON><PERSON><PERSON> moyenne (RMSE): {rmse_test:.2f} unités de CO2\")\n", "print(f\"  - Pas de surapprentissage détecté (différence train/test faible)\")\n", "\n", "print(f\"\\n=== MODÈLE IMPLÉMENTÉ FROM SCRATCH AVEC SUCCÈS ===\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 5}