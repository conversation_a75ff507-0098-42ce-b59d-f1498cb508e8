{"cells": [{"cell_type": "markdown", "id": "952e8adb", "metadata": {}, "source": ["# Regression Linéaire Simple du dataset Iris - Gradient Descent"]}, {"cell_type": "code", "execution_count": 95, "id": "4aaf4dbc", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "from sklearn.model_selection import train_test_split\n", "%matplotlib inline"]}, {"cell_type": "markdown", "id": "52aab10c", "metadata": {}, "source": ["## 1. Dataset\n", "Chargement du dataset Iris pour effectuer les prédictions de la largeur des pétales en fonction de longueur des pétales"]}, {"cell_type": "code", "execution_count": 96, "id": "fd4ad33b", "metadata": {}, "outputs": [], "source": ["dataset=pd.read_csv(\"iris.csv\")"]}, {"cell_type": "code", "execution_count": 97, "id": "b1933fdf", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>sepal_length</th>\n", "      <th>sepal_width</th>\n", "      <th>petal_length</th>\n", "      <th>petal_width</th>\n", "      <th>class</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>5.1</td>\n", "      <td>3.5</td>\n", "      <td>1.4</td>\n", "      <td>0.2</td>\n", "      <td>Iris-setosa</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>4.9</td>\n", "      <td>3.0</td>\n", "      <td>1.4</td>\n", "      <td>0.2</td>\n", "      <td>Iris-setosa</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>4.7</td>\n", "      <td>3.2</td>\n", "      <td>1.3</td>\n", "      <td>0.2</td>\n", "      <td>Iris-setosa</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4.6</td>\n", "      <td>3.1</td>\n", "      <td>1.5</td>\n", "      <td>0.2</td>\n", "      <td>Iris-setosa</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5.0</td>\n", "      <td>3.6</td>\n", "      <td>1.4</td>\n", "      <td>0.2</td>\n", "      <td>Iris-setosa</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   sepal_length  sepal_width  petal_length  petal_width        class\n", "0           5.1          3.5           1.4          0.2  Iris-setosa\n", "1           4.9          3.0           1.4          0.2  Iris-setosa\n", "2           4.7          3.2           1.3          0.2  Iris-setosa\n", "3           4.6          3.1           1.5          0.2  Iris-setosa\n", "4           5.0          3.6           1.4          0.2  Iris-setosa"]}, "execution_count": 97, "metadata": {}, "output_type": "execute_result"}], "source": ["dataset.head()"]}, {"cell_type": "code", "execution_count": 98, "id": "7b6ad56e", "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.plot(dataset[\"petal_width\"],dataset[\"petal_length\"],'o')\n", "plt.xlabel('petal_length')\n", "plt.ylabel('petal_width')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 99, "id": "dc5a27ae", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>petal_length</th>\n", "      <th>petal_width</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1.4</td>\n", "      <td>0.2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1.4</td>\n", "      <td>0.2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1.3</td>\n", "      <td>0.2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1.5</td>\n", "      <td>0.2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>1.4</td>\n", "      <td>0.2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>145</th>\n", "      <td>5.2</td>\n", "      <td>2.3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>146</th>\n", "      <td>5.0</td>\n", "      <td>1.9</td>\n", "    </tr>\n", "    <tr>\n", "      <th>147</th>\n", "      <td>5.2</td>\n", "      <td>2.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>148</th>\n", "      <td>5.4</td>\n", "      <td>2.3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>149</th>\n", "      <td>5.1</td>\n", "      <td>1.8</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>150 rows × 2 columns</p>\n", "</div>"], "text/plain": ["     petal_length  petal_width\n", "0             1.4          0.2\n", "1             1.4          0.2\n", "2             1.3          0.2\n", "3             1.5          0.2\n", "4             1.4          0.2\n", "..            ...          ...\n", "145           5.2          2.3\n", "146           5.0          1.9\n", "147           5.2          2.0\n", "148           5.4          2.3\n", "149           5.1          1.8\n", "\n", "[150 rows x 2 columns]"]}, "execution_count": 99, "metadata": {}, "output_type": "execute_result"}], "source": ["df=dataset[['petal_length','petal_width']]\n", "df"]}, {"cell_type": "code", "execution_count": 100, "id": "b5c2d3ff", "metadata": {}, "outputs": [], "source": ["\n", "x=dataset.petal_length\n", "y=dataset.petal_width\n"]}, {"cell_type": "code", "execution_count": 101, "id": "53cf6861", "metadata": {}, "outputs": [{"data": {"text/plain": ["(150,)"]}, "execution_count": 101, "metadata": {}, "output_type": "execute_result"}], "source": ["x.shape"]}, {"cell_type": "code", "execution_count": 102, "id": "df2012da", "metadata": {}, "outputs": [{"data": {"text/plain": ["0      0.2\n", "1      0.2\n", "2      0.2\n", "3      0.2\n", "4      0.2\n", "      ... \n", "145    2.3\n", "146    1.9\n", "147    2.0\n", "148    2.3\n", "149    1.8\n", "Name: petal_width, Length: 150, dtype: float64"]}, "execution_count": 102, "metadata": {}, "output_type": "execute_result"}], "source": ["y"]}, {"cell_type": "code", "execution_count": 103, "id": "96f1cead", "metadata": {}, "outputs": [{"data": {"text/plain": ["(150,)"]}, "execution_count": 103, "metadata": {}, "output_type": "execute_result"}], "source": ["y.shape"]}, {"cell_type": "markdown", "id": "107cf286", "metadata": {}, "source": ["Important: Après avoir vérifié les dimensions de x et y, On remarque que y n'a pas les dimensions (150, 1) et x n'a pas les dimensions (150,2). On corrige le problème avec np.reshape et values."]}, {"cell_type": "code", "execution_count": 104, "id": "eb4f57d7", "metadata": {}, "outputs": [], "source": ["x=dataset.petal_length.values.reshape(150,1)\n", "y=dataset.petal_width.values.reshape(150,1)"]}, {"cell_type": "code", "execution_count": 105, "id": "0594ec56", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(150, 1)\n", "(150, 1)\n"]}], "source": ["print(x.shape)\n", "print(y.shape)"]}, {"cell_type": "markdown", "id": "187f5058", "metadata": {}, "source": ["Création de la matrice X qui contient la colonne de Biais. Pour ca, on colle l'un contre l'autre le vecteur x et un vecteur 1 (avec np.ones) de dimension égale a celle de x"]}, {"cell_type": "code", "execution_count": 106, "id": "e8b25651", "metadata": {}, "outputs": [], "source": ["#X=np.hstack((np.ones((150,1)),x))\n", "X=np.hstack((x,(np.ones(x.shape))))"]}, {"cell_type": "code", "execution_count": 107, "id": "d1c71f2d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(150, 1)\n", "(150, 1)\n"]}], "source": ["print(x.shape)\n", "print(y.shape)"]}, {"cell_type": "code", "execution_count": null, "id": "039d154d", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 108, "id": "c857c0a4", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(120, 1)\n", "(30, 1)\n", "(120, 1)\n", "(30, 1)\n"]}], "source": ["# découpage du dataset en dataset d'entrainement et dataset de test\n", "x_train, x_test, y_train, y_test = train_test_split(x, y, test_size=0.2, random_state=42)\n", "print(x_train.shape)\n", "print(x_test.shape)\n", "print(y_train.shape)\n", "print(y_test.shape)"]}, {"cell_type": "code", "execution_count": 109, "id": "97825a6d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(120, 2)\n", "(30, 2)\n", "(120, 1)\n", "(30, 1)\n"]}], "source": ["X_train=np.hstack((x_train,(np.ones(x_train.shape))))\n", "X_test=np.hstack((x_test,(np.ones(x_test.shape))))\n", "print(X_train.shape)\n", "print(X_test.shape)\n", "print(y_train.shape)\n", "print(y_test.shape)"]}, {"cell_type": "markdown", "id": "1f5703e5", "metadata": {}, "source": ["Finalement, création d'un vecteur parametre θ, initialisé avec des coefficients aléatoires. Ce vecteur est de dimension (2, 1). Si on désire toujours reproduire le meme vecteur \n", "θ, on utilise comme avant np.random.seed(0)"]}, {"cell_type": "code", "execution_count": 110, "id": "e45f2b9a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Theta: [[1.76405235]\n", " [0.40015721]]\n"]}], "source": ["np.random.seed(0) # pour produire toujours le meme vecteur theta aléatoire\n", "theta=np.random.randn(2,1)\n", "print('Theta:',theta)"]}, {"cell_type": "markdown", "id": "41a0e7d5", "metadata": {}, "source": ["## 2. <PERSON><PERSON><PERSON><PERSON>\n", "On implémente un modèle selon l'équation matricielle F=X.θ et puis on teste le modèle initiale défini par la valeur initiale de θ qu'on a initialisé d'une manière aléatoire.\n"]}, {"cell_type": "code", "execution_count": 111, "id": "bc06c159", "metadata": {}, "outputs": [], "source": ["def model(X, theta):\n", "    return X.dot(theta)"]}, {"cell_type": "code", "execution_count": 112, "id": "0e86cdd4", "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAjMAAAGxCAYAAACXwjeMAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjUuMiwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy8qNh9FAAAACXBIWXMAAA9hAAAPYQGoP6dpAABQC0lEQVR4nO3dd3gU5fo38O9uSC8bEkmygZBEqTH0oiBduhSxIb2pcA4g5ShFRIgiEVHkiIqAvoAixp8ighhQpFpAIPTegkFMTqSYShKy+7x/5GQPmy3ZvjO738915brYmZ2Ze5ayN/f9zPMohBACRERERDKldHcARERERPZgMkNERESyxmSGiIiIZI3JDBEREckakxkiIiKSNSYzREREJGtMZoiIiEjWmMwQERGRrNVwdwDOptVq8eeffyI0NBQKhcLd4RAREZEFhBAoKChAbGwslErztRePT2b+/PNPxMXFuTsMIiIissHVq1dRp04ds+/x+GQmNDQUQMWHERYW5uZoiIiIyBL5+fmIi4vTfY+b4/HJTGVrKSwsjMkMERGRzFgyRIQDgImIiEjWmMwQERGRrDGZISIiIlnz+DEzltJoNLhz5467wyCSFF9fX/j4+Lg7DCIis7w+mRFCICcnB3///be7QyGSpPDwcMTExHCeJiKSLK9PZioTmaioKAQFBfEfbKL/EkKguLgYubm5AAC1Wu3miIiIjPPqZEaj0egSmcjISHeHQyQ5gYGBAIDc3FxERUWx5UREkuTVA4Arx8gEBQW5ORIi6ar8+8ExZUQkVV6dzFRia4nINP79ICKpYzJDZIXt27dj2bJl7g6DiIjuwmTGg3Tp0gVTp051dxge69q1axg/fjy++OILrF+/3t3hEBHRfzGZcRCNVmDfpRvYdPQa9l26AY1WuDukau3evRsKhcLlj6XPnz8fzZs3d8m11qxZg/DwcIeca/z48Vi2bBk2btyIN998Ezk5OQ45rzuNHj0ajz76qLvDICKyi1c/zeQo205mI+Xb08jOK9FtU6sCMK9/Enon83FWOSgrK4Ofn5/Z92zZskX366NHjzo5IiIiGSgvB778EnjsMcDf321hsDJjp20ns/GPdYf1EhkAyMkrwT/WHca2k9lOuW5RURFGjhyJkJAQqNVqvP322wbvWbduHVq3bo3Q0FDExMRg6NChujlDrly5gq5duwIAatasCYVCgdGjR1fc07Zt6NChA8LDwxEZGYl+/frh0qVLuvOWlZVh0qRJUKvVCAgIQEJCAlJTU3X78/Ly8NxzzyEqKgphYWHo1q0bjh07BqCiUpKSkoJjx45BoVBAoVBgzZo1Ju9z9erVaNy4MQICAtCoUSN88MEHun1XrlyBQqHA119/ja5duyIoKAjNmjXDvn37AFRUnsaMGYO8vDzdtebPnw8ASEhIwIIFCzB69GioVCo8++yzAICZM2eiQYMGCAoKwr333ou5c+fqPcVTtapUWdl46623oFarERkZiYkTJ+odU1ZWhhkzZqB27doIDg7GAw88gN27d+v2V1aPtmzZgoYNGyIoKAhPPPEEioqKsHbtWiQkJKBmzZqYPHkyNBqN1ef9/vvv0bhxY4SEhKB3797Izs7W3cvatWuxadMm3edz9/FERGaNGAH4+gJDhwIBAW4NhZUZO2i0AinfnoaxhpIAoACQ8u1p9EiKgY/SsU+EvPjii9i1axc2btyImJgYvPTSS8jIyND7oi0rK8Nrr72Ghg0bIjc3F9OmTcPo0aORnp6OuLg4bNiwAY8//jjOnTuHsLAw3ZwiRUVFmD59Opo0aYKioiK88sorGDRoEI4ePQqlUol3330Xmzdvxv/93/+hbt26uHr1Kq5evVpx30LgkUceQUREBNLT06FSqbBixQo8/PDDOH/+PAYPHoyTJ09i27Zt+PHHHwEAKpXK6D2uWrUK8+bNw3vvvYcWLVrgyJEjePbZZxEcHIxRo0bp3jdnzhy89dZbqF+/PubMmYMhQ4bg4sWLaN++PZYuXYpXXnkF586dAwCEhITojlu8eDHmzp2Ll19+WbctNDQUa9asQWxsLE6cOIFnn30WoaGhmDFjhsnfi127dkGtVmPXrl24ePEiBg8ejObNm+sSpDFjxuDKlStIS0tDbGwsNm7ciN69e+PEiROoX78+AKC4uBjvvvsu0tLSUFBQgMceewyPPfYYwsPDkZ6ejsuXL+Pxxx9Hhw4dMHjwYKvO+9Zbb+HTTz+FUqnE8OHD8cILL+Czzz7DCy+8gDNnziA/Px+rV68GAERERFT3R4+IvN3Fi8B//43RGT7cPbFUEm60Z88e0a9fP6FWqwUAsXHjRt2+srIyMWPGDJGcnCyCgoKEWq0WI0aMENeuXbPqGnl5eQKAyMvLM9h3+/Ztcfr0aXH79m2b4v/14nURP3NLtT+/Xrxu0/lNKSgoEH5+fiItLU237caNGyIwMFBMmTLF5HEHDhwQAERBQYEQQohdu3YJAOLWrVtmr5ebmysAiBMnTgghhJg8ebLo1q2b0Gq1Bu/dsWOHCAsLEyUlJXrb77vvPrFixQohhBDz5s0TzZo1q/Y+4+LixPr16/W2vfbaa6Jdu3ZCCCEyMzMFAPHRRx/p9p86dUoAEGfOnBFCCLF69WqhUqkMzh0fHy8effTRamN48803RatWrXSvq8Y+atQoER8fL8rLy3XbnnzySTF48GAhhBAXL14UCoXC4M/tww8/LGbPnq2LEYC4ePGibv/48eNFUFCQ7vdKCCF69eolxo8fb9d533//fREdHa0X/8CBA81+Bvb+PSEiDwIY/syf75RLmfv+rsqtlZmioiI0a9YMY8aMweOPP663r7i4GIcPH8bcuXPRrFkz3Lp1C1OnTsWAAQNw6NAhN0WsL7egpPo3WfE+S126dAllZWVo166dbltERAQaNmyo974jR45g/vz5OHr0KG7evAmtVgsAyMrKQlJSktnzz507F/v378f169f1jktOTsbo0aPRo0cPNGzYEL1790a/fv3Qs2dPAEBGRgYKCwsNZlS+ffu2XquqOn/99ReuXr2KcePG6SocAFBeXm5QyWnatKnu15VT7ufm5qJRo0Zmr9G6dWuDbV999RWWLl2KixcvorCwEOXl5QgLCzN7nvvvv19vZly1Wo0TJ04AAA4fPgwhBBo0aKB3TGlpqd5nFBQUhPvuu0/3Ojo6GgkJCXqVpOjoaF2b0NbzqtVq3TmIiCy2ciUwfrzhdq0WkMBcVG5NZvr06YM+ffoY3adSqbB9+3a9bcuWLUPbtm2RlZWFunXruiJEs6JCLesRWvo+SwlR/ZNSRUVF6NmzJ3r27Il169ahVq1ayMrKQq9evVBWVmb22P79+yMuLg6rVq1CbGwstFotkpOTdce1bNkSmZmZ2Lp1K3788Uc89dRT6N69O7766itotVqo1WqjYy+seaqoMoFatWoVHnjgAb19VafU9/X11f26coK3yuPNCQ4O1nu9f/9+PP3000hJSUGvXr2gUqmQlpZmdDySqetXxlB5fa1WCx8fH2RkZBjEfXeiYuwczjqvJX9+iIgAALduAcbaz4cOAa1auT4eE2Q1ZqZyIKejHrW1V9vECKhVAcjJKzE6bkYBIEYVgLaJjh2HUK9ePfj6+mL//v26pO7WrVs4f/48OnfuDAA4e/Ysrl+/jjfeeANxcXEAYFDRqnx65+5BpTdu3MCZM2ewYsUKdOzYEQDw888/G8QQFhaGwYMHY/DgwXjiiSfQu3dv3Lx5Ey1btkROTg5q1KiBhIQEo/H7+fnpXdOY6Oho1K5dG5cvX8awYcMs+FSMs+RalX755RfEx8djzpw5um2///67zdcGgBYtWkCj0SA3N1f3eTqCo85rzedDRF7GWMWlUydgzx7Xx1IN2SQzJSUlmDVrFoYOHWq27F9aWorS0lLd6/z8fKfF5KNUYF7/JPxj3WEoAL2EpvKPwLz+SQ4f/BsSEoJx48bhxRdfRGRkJKKjozFnzhwolf97OK1u3brw8/PDsmXLMGHCBJw8eRKvvfaa3nni4+OhUCiwZcsW9O3bF4GBgahZsyYiIyOxcuVKqNVqZGVlYdasWXrHvfPOO1Cr1WjevDmUSiW+/PJLxMTEIDw8HN27d0e7du3w6KOPYtGiRWjYsCH+/PNPpKen49FHH0Xr1q2RkJCAzMxMHD16FHXq1EFoaCj8jTzSN3/+fDz//PMICwtDnz59UFpaikOHDuHWrVuYPn26RZ9VQkICCgsLsWPHDjRr1gxBQUEm1+KqV68esrKykJaWhjZt2uC7777Dxo0bLbqOKQ0aNMCwYcMwcuRIvP3222jRogWuX7+OnTt3okmTJujbt69bz5uQkIDvv/8e586dQ2RkJFQqlUE1h4i8zJdfAk89Zbi9uBj474MiUiOLR7Pv3LmDp59+GlqtVu/RXGNSU1OhUql0P5VVCWfpnazG8uEtEaPSbyXFqAKwfHhLp80zs3jxYnTq1AkDBgxA9+7d0aFDB7S6q+RXq1YtrFmzBl9++SWSkpLwxhtv4K233tI7R+3atZGSkoJZs2YhOjoakyZNglKpRFpaGjIyMpCcnIxp06Zh8eLFeseFhIRg0aJFaN26Ndq0aYMrV64gPT0dSqUSCoUC6enp6NSpE8aOHYsGDRrg6aefxpUrVxAdHQ0AePzxx9G7d2907doVtWrVwueff270Hp955hl89NFHWLNmDZo0aYLOnTtjzZo1SExMtPhzat++PSZMmIDBgwejVq1aePPNN02+d+DAgZg2bRomTZqE5s2b49dff8XcuXMtvpYpq1evxsiRI/Gvf/0LDRs2xIABA/Dbb7/Z/WfTEed99tln0bBhQ7Ru3Rq1atXCL7/8YldMRCRjQlRUY6omMtOmVeyTaCIDAAohkQa6QqHAxo0bDWYjvXPnDp566ilcvnwZO3fuNBhYWpWxykxcXBzy8vIMKjolJSXIzMxEYmIiAux8Rl6jFTiQeRO5BSWICq1oLTm6IkPkDo78e0JEEtWqFXD4sOF2N6YI+fn5UKlURr+/q5J0m6kykblw4QJ27dpVbSIDAP7+/kZbFs7mo1Sg3X3Vx0dERCQZFy4AVZ6KBAAcPw40aeL6eGzk1mSmsLAQFy9e1L2uHEcRERGB2NhYPPHEEzh8+DC2bNkCjUajWwsnIiKi2qnniYiIyAxjA3zr1AH+OwmqnLh1zMyhQ4fQokULtGjRAgAwffp0tGjRAq+88gr++OMPbN68GX/88QeaN28OtVqt+/n111/dGTYREZF8LVhgPJEpL5dlIgO4uTLTpUsXs3NeSGQ4DxERkfyVlBgfxPvRR8C4ca6Px4EkPWaGiIiIHMDULL0eUjSQxaPZREREZIO9e40nMtnZHpPIAKzMEBEReSZjScxjjwEbNrg+FidjZYZs8t5773EgNhGRFD39tPFERgiPTGQAJjNkgS5dumDq1Kl621q2bIkhQ4bg2rVrBu9fs2aN3etnjR492mACxeooFAp88803Dj3vlStXoFAocPToUatiISJyudzciiTmiy/0t+/c6VEtJWPYZiKbtG/fHkuXLsWQIUOwc+dO1Kjh2D9K//73v61+mi07Oxs1a9YEUJGEJCYm4siRI2jevLld5yUikjwPH+BbHVZmyGaDBg3C3r17HZ7IAIBKpbK6uhMTE1Pt7M+2nJeISLLmzzeeyBQXe00iAzCZka0uXbpg8uTJmDp1KmrWrIno6GisXLkSRUVFGDNmDEJDQ3Hfffdh69atesft2bMHbdu2hb+/P9RqNWbNmoXy8nLd/qKiIowcORIhISFQq9V4++23Da5dVlaGGTNmoHbt2ggODkbbtm2xfft2s/F+++23aNWqFQICAnDvvfciJSVF77pVVW0HdenSBc8//zxmzJiBiIgIxMTEYP78+XrH3N1mqlyMskWLFlAoFOjSpYvR827btg0dOnRAeHg4IiMj0a9fP1y6dMnsvRARuZ1GU5HEpKTob583T/KLQjoDkxkZW7t2Le655x4cOHAAkydPxj/+8Q88+eSTaN++PQ4fPoxevXphxIgRKC4uBgBcu3YNffv2RZs2bXDs2DEsX74cH3/8MRYsWKA754svvohdu3Zh48aN+OGHH7B7925kZGToXXfMmDHYt28fvvjiCxw/fhxDhgxBv379cOrUKaNxfv/99xg+fDief/55nD59GitWrMCaNWvw+uuvW32/wcHB+O233/Dmm2/i1VdfNZlEHThwAADw448/Ijs7G19//bXR9xUVFWH69Ok4ePAgduzYAaVSiUGDBkGr1VoVGxGRyygUgLGKuBAVlRpvJDxcXl6eACDy8vIM9t2+fVucPn1a3L59+38btVohCgtd/6PVWnVfnTt3Fh06dNC9Li8vF8HBwWLEiBG6bdnZ2QKA2LdvnxBCiJdeekk0bNhQaO+61vvvvy9CQkKERqMRBQUFws/PT6Slpen237hxQwQGBoopU6YIIYS4ePGiUCqVIicnRy+eHj16iBdffFEIIcTq1auFSqXS7evYsaNYuHCh3vs//fRToVarTd7fqFGjxMCBA03erxBCtGnTRsycOVP3GoDYuHGjEEKIzMxMAUAcOXLE7Hmrys3NFQDEiRMnzJ7Hmxj9e0JErvfLL0JUpCz6PxkZ7o7MKcx9f1fFAcBVFRcDISGuv25hIRAcbNUhTZs21f3ax8cHkZGRaHLXKqfR0dEAgNzcXADAmTNn0K5dOyju6q8+9NBDKCwsxB9//IFbt26hrKwM7dq10+2PiIhAw4YNda8PHz4MrVaLmJgYg3hMLdGekZGBgwcP6lViNBoNSkpKUFxcjKCgIKvvFwDUarXu3mx16dIlzJ07F/v378f169d1FZmsrCwkJyfbdW4iIofx8gG+1WEyI2O+vr56rxUKhd62yqSl8gtaCKGXyFRuq3yvsOAvhVarhY+PD27fvm1wfXPHpKSk4LHHHjPYFxAQYNE5AOP3a287qH///oiLi8OqVasQGxsLrVaL5ORklJWV2XVeIiKH6NsXqDL2EUDFmBklR4pUYjJTVVBQRZXEHdd1sqSkJGzYsEEvqfn1118RGhqK2rVro2bNmvD19cX+/ftRt25dAMCtW7dw/vx5dO7cGUDFgFqNRoM9e/age/fuFl23ZcuWOHfuHOrVq+ecGzPCz88PQEUFyJQbN27gzJkzWLFiBTp27AgA+Pnnn10SHxGRWX//Dfx3qgk9b74JvPiiy8OROiYzVSkUVrd75OKf//wnli5dismTJ2PSpEk4d+4c5s2bh+nTp0OpVCIkJATjxo3Diy++iMjISERHR2POnDlQ3pX9N2jQAMOGDcOYMWOwZMkStGrVCjdu3MD333+Ppk2bYsCAAQbXfeWVV9CvXz/ExcXhySefhFKpxPHjx3HixAm9wceOFBUVhcDAQGzbtg116tRBQEAAVCqV3ntq1qyJyMhIrFy5Emq1GllZWZg1a5ZT4iEishhbSlZjjcqL1K5dG+np6Thw4ACaNWuGCRMmYNy4cXj55Zd171m8eDE6deqEAQMGoHv37ujQoQNatWqld57Vq1dj7NixmDFjBho1aoQBAwbg8OHDusehq+rVqxe2bNmC7du3o02bNnjwwQexZMkSxMfHO+1ea9SogXfffRcrVqxAbGwsBg4caPAepVKJtLQ0ZGRkIDk5GdOmTcPixYudFhMRkVlr1njFopDOoBCWDJSQsfz8fKhUKuTl5RkMUC0pKUFmZiYSExOtGrtB5E3494TIyYQwPv6lSRPg+HHXxyMR5r6/q2KbiYiIyF3YUnIItpmIiIhcbdcu44nMd98xkbEBKzNERESuxGqMw7EyQ0RE5Ao1ahhPZPLzmcjYiZUZIiIiZyouNj3lB5MYh2AyA1g08y2Rt+LfDyI7sKXkEl7dZqqcHr9yVWkiMlT598PS5SuICMBzzxlPZLZuZSLjBF5dmfHx8UF4eLhuscKgoCCDtYuIvJUQAsXFxcjNzUV4eDh8fHzcHRKRPLAa43JencwA0K3+bO/qy0SeKjw83Ogq6URUhakkRqs1vY8cwuuTGYVCAbVajaioKNy5c8fd4RBJiq+vLysyRNU5fBiosuwLACA2Frh2zfXxeCGvT2Yq+fj48B9tIiKyDltKkuDVA4CJiIhsolAYT2TOnmUi4waszBAREVlKqwVMVfGZxLgNkxkiIiJLsKUkWWwzERERmbN4sfFEZuJEJjISwcoMERGRKazGyAIrM0RERFWZGuBbVMRERoKYzBAREVXKzjZfjQkKcm08ZBG2mYiIiAC2lGSMlRkiIvJusbHGE5kvv2QiIxOszBARkfdiNcYjMJkhIiLvwyTGo7DNRERE3mPTJuOJjFLJREbGWJkhIiLvwGqMx2JlhoiIPJupOWMuXmQi4yFYmSEiIs9UUgIEBhrfxyTGozCZISIiz8OWkldhm4mIiDxHr17GE5lBg5jIeDBWZoiIyDOwGuO13FqZ2bt3L/r374/Y2FgoFAp88803evuFEJg/fz5iY2MRGBiILl264NSpU+4JloiIpMnUAN/SUiYyXsKtyUxRURGaNWuG9957z+j+N998E0uWLMF7772HgwcPIiYmBj169EBBQYGLIyUiIsk5dMh8NcbPz7XxkNu4tc3Up08f9OnTx+g+IQSWLl2KOXPm4LHHHgMArF27FtHR0Vi/fj3Gjx/vylCJiEhK2FKiu0h2AHBmZiZycnLQs2dP3TZ/f3907twZv/76q8njSktLkZ+fr/dDREQewlRLafVqJjJeTLIDgHNycgAA0dHRetujo6Px+++/mzwuNTUVKSkpTo2NiIjcgNUYMkGylZlKiip/eIUQBtvuNnv2bOTl5el+rl696uwQiYjImUxVY4RgIkMAJJzMxMTEAPhfhaZSbm6uQbXmbv7+/ggLC9P7ISIiGZowgdUYsohkk5nExETExMRg+/btum1lZWXYs2cP2rdv78bIiIjI6RQKYMUKw+2sxpARbh0zU1hYiIsXL+peZ2Zm4ujRo4iIiEDdunUxdepULFy4EPXr10f9+vWxcOFCBAUFYejQoW6MmoiInMZUJebyZSAx0bWxkGy4NZk5dOgQunbtqns9ffp0AMCoUaOwZs0azJgxA7dv38Y///lP3Lp1Cw888AB++OEHhIaGuitkIiJyhr/+AqKijO9jJYaqoRDCs/+U5OfnQ6VSIS8vj+NniIikiONiyAhrvr8lO2aGiIg8nKmnlHr3ZiJDVpHsPDNEROTBWI0hB2JlhoiIXMdUNUajYSJDNmMyQ0REzvfpp+arMUp+HZHt2GYiIiLnYkuJnIypMBEROYepltJnnzGRIYdiZYaIiBxLowFqmPh6YRJDTsBkhoiIHIctJXIDtpmIiMh+9eoxkSG3YWWGiIjswySG3IyVGSIiso2pAb7/+Q8TGXIpJjNERGSdY8fMV2NMLRhJ5CRsMxERkeXYUiIJYmWGiIiqZ6ql1L49ExlyO1ZmiIjIPFZjSOKYzBARkXFMYkgm2GYiIiJ9Tz/NRIZkhZUZIiL6HyYxJEOszBARkekBvl9+yUSGJI+VGSIib1ZQAISFGd/HJIZkgskMEZG3YkuJPASTGSIib2MqiQGYyJAsMZkhIvImrMaQB+IAYCIib2BqgG9eHhMZkj0mM0REnuyzz8xXY0wN/iWSEbaZiIg8FVtK5CVYmSEi8jSmWkqdOjGRIY/EygwRkSdhNYa8EJMZIiJPwCSGvBjbTEREcubvz0SGvB4rM0REcsUkhggAKzNERPJjaoDv7t1MZMgrsTJDRCQXv/8OJCQY38ckhrwYkxkiIjlgS4nIJCYzRERSxkUhiarFZIaISKpYjSGyCAcAExFJjakBvnfuMJEhMoLJDBGRVMyYYb4aU4PFdCJj+DeDiEgK2FIishkrM0RE7mSqpTR+PBMZIguxMkNE5A4ajem2EZMYIqswmSEicjW2lIgciskMEZGrcM4YIqdgMkNE5AqsxhA5jeQHAJeXl+Pll19GYmIiAgMDce+99+LVV1+FVqt1d2hERNUzNcD37FkmMkQOIvnKzKJFi/Dhhx9i7dq1uP/++3Ho0CGMGTMGKpUKU6ZMcXd4RETG7dsHtG9vfB+TGCKHknwys2/fPgwcOBCPPPIIACAhIQGff/45Dh065ObIiIhMYEuJyKUk32bq0KEDduzYgfPnzwMAjh07hp9//hl9+/Z1c2RERFWYaikBTGSInEjylZmZM2ciLy8PjRo1go+PDzQaDV5//XUMGTLE6PtLS0tRWlqqe52fn++qUInImzGJIXIbyVdmvvjiC6xbtw7r16/H4cOHsXbtWrz11ltYu3at0fenpqZCpVLpfuLi4lwcMRF5FVPVGCGYyBC5iEIIaf9ti4uLw6xZszBx4kTdtgULFmDdunU4e/aswfuNVWbi4uKQl5eHsLAwl8RMRF6gTx9g2zbj+6T9zyqRLOTn50OlUln0/S35NlNxcTGUSv0Cko+Pj8lHs/39/eHv7++K0IjIW7GlRCQpkm8z9e/fH6+//jq+++47XLlyBRs3bsSSJUswaNAgd4dGRN7GVEvpvfeYyBC5keTbTAUFBZg7dy42btyI3NxcxMbGYsiQIXjllVfg5+dX7fHWlKmIiIwqKgJCQozvk/Y/oUSyZc33t+STGXsxmSEiu7ClROQWHjVmhojILbgoJJFsMJkhIqqK1RgiWZH8AGAiIpcxNcD35k0mMkQSxmSGiGjDBvPVmJo1XRsPEVmFbSYi8m5sKRHJHiszROSdTLWUOnRgIkMkM6zMEJH3YTWGyKMwmSEi78Ekhsgj2ZzM7NixAzt27EBubq7BOkn/7//9P7sDIyJymMaNASML0wJgIkPkAWxKZlJSUvDqq6+idevWUKvVUJibXIqIyJ1YjSHyeDYlMx9++CHWrFmDESNGODoeIiLHMJXE7NoFdOni0lCIyLlsSmbKysrQvn17R8dCRGS/nBxArTa+j9UYIo9k06PZzzzzDNavX+/oWIiI7KNQGE9khGAiQ+TBLK7MTJ8+XfdrrVaLlStX4scff0TTpk3h6+ur994lS5Y4LkIioupwUUgir2ZxMnPkyBG9182bNwcAnDx50qEBERFZhQN8ibyexcnMrl27nBkHEZF1TCUxZWVAlWoxEXk2m8bMjB07FgUFBQbbi4qKMHbsWLuDIiIy6e23zVdjmMgQeR2FENbXYn18fJCdnY2oqCi97devX0dMTAzKy8sdFqC98vPzoVKpkJeXh7CwMHeHQ0T2YEuJyGtY8/1tVWUmPz8feXl5EEKgoKAA+fn5up9bt24hPT3dIMEhIrKbqUUhp0xhIkNE1s0zEx4eDoVCAYVCgQYNGhjsVygUSElJcVhwROTlhACUJv7PxSSGiP7LqmRm165dEEKgW7du2LBhAyIiInT7/Pz8EB8fj9jYWIcHSUReiC0lIrKQVclM586dAQCZmZmoW7cu12QiIsfjnDFEZCWLk5njx4/rvT5x4oTJ9zZt2tT2iIjIe7EaQ0Q2sDiZad68ORQKBYQQ1VZkNBqN3YERkRcx9W/KuXOAkfF5RER3s/hppszMTFy+fBmZmZnYsGEDEhMT8cEHH+DIkSM4cuQIPvjgA9x3333YsGGDM+MlIk9y/Lj5agwTGSKygMWVmfj4eN2vn3zySbz77rvo27evblvTpk0RFxeHuXPn4tFHH3VokETkgdhSIiIHsWkG4BMnTiAxMdFge2JiIk6fPm13UETkwUzNGaNUMpEhIpvYlMw0btwYCxYsQElJiW5baWkpFixYgMaNGzssOCLyMOaqMRxrR0Q2surR7Eoffvgh+vfvj7i4ODRr1gwAcOzYMSgUCmzZssWhARKRBzCVxGi15h/FJiKygE1rMwFAcXEx1q1bh7Nnz0IIgaSkJAwdOhTBwcGOjtEuXJuJyI2eeQb4+GPj+9hSIiIzrPn+tqkyAwBBQUF47rnnbD2ciDwdB/gSkYtYnMxs3rwZffr0ga+vLzZv3mz2vQMGDLA7MCKSKVNJzIcfAuPHuzYWIvIKFreZlEolcnJyEBUVBaWphd9QsdiklCbNY5uJyEVKSoDAQOP7WI0hIis5pc2k1WqN/pqIiC0lInInmx7NLi4udnQcRCRHpuaMAZjIEJHL2DQAODw8HK1bt0aXLl3QuXNndOjQQXJPMRGRkzGJISKJsKkys2fPHgwYMACHDx/Gk08+iZo1a+LBBx/ErFmzsHXrVkfHSERSYqoac/06Exkicgub55mppNFocPDgQXz44Yf47LPPoNVqOQCYyBP98APQq5fxfUxiiMjBXDLPzNmzZ7F7927s2bMHu3fvxp07d9C/f3907tzZ1lMSkVSxpUREEmZTMhMTE4M7d+6gW7du6NKlC1566SU0adLE0bERkbuZSmI6dQL27HFtLEREJtg0ZiYmJgaFhYXIyspCVlYW/vjjDxQWFjo6NiJyJ3PVGCYyRCQhNiUzR48exX/+8x/MmTMH5eXlmDt3LmrVqoUHHngAs2bNcnSMRORKpgb4CsG2EhFJkt0DgG/evIndu3dj06ZNWL9+PQcAE8lVu3bA/v3G9zGJISIXc/oA4I0bN2L37t3YvXs3Tp06hcjISHTs2BHvvPMOunbtalPQRORGHOBLRDJmU2UmKioKnTp1QpcuXdClSxckJyc7IzaHYGWGyAxTScyPPwIPP+zaWIiI7mLN97dNY2Zyc3Px1VdfYdKkSWYTmTfeeAN///23LZfQc+3aNQwfPhyRkZEICgpC8+bNkZGRYfd5ibzW9evmqzFMZIhIRmxKZiy1cOFC3Lx5065z3Lp1Cw899BB8fX2xdetWnD59Gm+//TbCw8MdEySRt1EogFq1DLdzgC8RyZTNk+ZZws6xxQCARYsWIS4uDqtXr9ZtS0hIsPu8RF7HVCUGYBJDRLLm1MqMI2zevBmtW7fGk08+iaioKLRo0QKrVq0y+f7S0lLk5+fr/RB5PXMtJSYyRCRzkk9mLl++jOXLl6N+/fr4/vvvMWHCBDz//PP45JNPjL4/NTUVKpVK9xMXF+fiiIkkxNScMSUlTGKIyGPYPc+MOaGhoTh27Bjuvfdem8/h5+eH1q1b49dff9Vte/7553Hw4EHs27fP4P2lpaUoLS3Vvc7Pz0dcXByfZiLvsmIFMGGC8X1MYohIBlyy0KSrqNVqJCUl6W1r3LgxNmzYYPT9/v7+8Pf3d0VoRNLEOWOIyMs4tc3UsWNHBAYG2nWOhx56COfOndPbdv78ecTHx9t1XiKPY6qlNHkyExki8mgWV2asGUhbWQ5KT0+3PqIqpk2bhvbt22PhwoV46qmncODAAaxcuRIrV660+9xEHkEIQGni/yVMYojIC1g8ZkapVEJh7tFOVDyKrVAoHL4205YtWzB79mxcuHABiYmJmD59Op599lmLjuUMwOTR2FIiIg/llDEzu3btsjswW/Xr1w/9+vVz2/WJJMfXFygvN76PiQwReRmLk5nOnTs7Mw4ishSrMUREeux6mqm4uBhZWVkoKyvT2960aVO7giIiI0wlMadOAVWe+CMi8iY2JTN//fUXxowZg61btxrd7+gxM0Re7cIFoEED4/tYjSEisu3R7KlTp+LWrVvYv38/AgMDsW3bNqxduxb169fH5s2bHR0jkfdSKIwnMlyGgIhIx6bKzM6dO7Fp0ya0adMGSqUS8fHx6NGjB8LCwpCamopHHnnE0XESeRdTLaWQEKCgwLWxEBFJnE2VmaKiIkRFRQEAIiIi8NdffwEAmjRpgsOHDzsuOiJvZG6ALxMZIiIDNiUzDRs21M3K27x5c6xYsQLXrl3Dhx9+CLVa7dAAibyGqRl8tVq2lIiIzLB5zEx2djYAYN68edi2bRvq1q2Ld999FwsXLnRogEQeb+ZM89WYaiarJCLydg5ZNbu4uBhnz55F3bp1cc899zgiLofhDMAkaZwzhojIKGu+v22qzLz66qsoLi7WvQ4KCkLLli0RHByMV1991ZZTEnkXUy2lFSuYyBARWcmmyoyPjw+ys7N1g4Ar3bhxA1FRUZKaZ4aVGZKUsjLA39/4PiYxREQ6Tlmb6W6VC0pWdezYMURERNhySiLPx5YSEZFTWJXM1KxZEwqFAgqFAg0aNNBLaDQaDQoLCzFhwgSHB0kka+YG8DKRISKym1XJzNKlSyGEwNixY5GSkgKVSqXb5+fnh4SEBLRr187hQRLJFqsxREROZ1UyM2rUKABAYmIiHnroIdSoYdc6lUSey1QSk5sL1Krl2liIiDycTU8zde7cGb///jtefvllDBkyBLm5uQCAbdu24dSpUw4NkEhW9u41X41hIkNE5HA2JTN79uxBkyZN8Ntvv+Hrr79GYWEhAOD48eOYN2+eQwMkkg2FAujc2XA7F4UkInIqm5KZWbNmYcGCBdi+fTv8/Px027t27Yp9+/Y5LDgiWTA1Z0zHjkxiiIhcwKZBLydOnMD69esNtteqVQs3btywOygi2eAAXyIit7OpMhMeHq5bm+luR44cQe3ate0OikjyTFVj2FIiInI5m5KZoUOHYubMmcjJyYFCoYBWq8Uvv/yCF154ASNHjnR0jETS0b8/qzFERBJj03IGd+7cwejRo5GWlgYhBGrUqIHy8nIMGzYMa9asgY+PjzNitQmXMyCHYRJDROQy1nx/27Vq9uXLl3Ho0CEoFAq0aNEC9erVs/VUTsNkhuxmKonZtg3o1cu1sRAReQmnr80EAB9//DHeeecdXLhwAQBQv359TJ06Fc8884ytpySSlr//BmrWNL6P1RgiIsmwKZmZO3cu3nnnHUyePFm3fMG+ffswbdo0XLlyBQsWLHBokEQux5YSEZFs2NRmuueee7Bs2TIMGTJEb/vnn3+OyZMn4/r16w4L0F5sM5FVuCgkEZEkOL3NpNFo0Lp1a4PtrVq1Qnl5uS2nJHI/VmOIiGTJpkezhw8fjuXLlxtsX7lyJYYNG2Z3UEQuZWrOmOJiJjJERDJg1wDgH374AQ8++CAAYP/+/bh69SpGjhyJ6dOn6963ZMkS+6MkcoZ164ARI4zvYxJDRCQbNiUzJ0+eRMuWLQEAly5dAlCxlEGtWrVw8uRJ3fsU5sYfELkTW0pERB7DpmRm165djo6DyDVMJTETJwLvvefaWIiIyCFsbjMRyYoQgNLEEDFWY4iIZI3JDHk+tpSIiDyaTU8zEclC7dpMZIiIvAArM+SZmMQQEXkNVmbIs5iaM+bkSSYyREQeipUZ8gyXLgGmVm1nEkNE5NGYzJD8saVEROTVmMyQfHFRSCIiApMZkitWY4iI6L84AJjkxdQAX42GiQwRkZdiMkPy8Oqr5qsxpmb3JSIij8c2E0kfW0pERGQG/ztL0mWqpbRsGRMZIiLSkVUyk5qaCoVCgalTp7o7FHKm8nLz1ZhJk1wbDxERSZps2kwHDx7EypUr0bRpU3eHQs7ElhIREVlJFpWZwsJCDBs2DKtWrULNmjXdHQ45g6mWEsBEhoiIzJJFMjNx4kQ88sgj6N69u7tDIWcwl8QwkSEiompIvs2UlpaGjIwMHDp0yKL3l5aWorS0VPc6Pz/fWaGRvUwlMdnZQEyMa2MhIiLZknRl5urVq5gyZQo+++wzBAQEWHRMamoqVCqV7icuLs7JUZLV9u0zX41hIkNERFZQCCHdOv4333yDQYMGwcfHR7dNo9FAoVBAqVSitLRUbx9gvDITFxeHvLw8hIWFuSx2MoHjYoiIyAL5+flQqVQWfX9Lus308MMP48SJE3rbxowZg0aNGmHmzJkGiQwA+Pv7w9/f31UhkqVMJTFt2gAHDrg2FiIi8iiSTmZCQ0ORnJysty04OBiRkZEG20nCWI0hIiInknQyQzLHJIaIiFxAdsnM7t273R0CVeeJJ4ANG4zvYyJDREQOJrtkhiSO1RgiInIxST+aTTJiagbfzZuZyBARkVOxMkP2KSgATD0yxySGiIhcgMkM2Y4tJSIikgAmM2Q9U0kMwESGiIhcjskMWYfVGCIikhgOACbLmBrgW1jIRIaIiNyKyQyZ9+WX5qsxwcGujYeIiKgKtpnINLaUiIhIBliZIUOmWkrjxjGRISIiyWFlhvSxGkNERDLDZIYqMIkhIiKZYpvJ2z3wABMZIiKSNVZmvBmTGCIi8gCszHgjUwN8jx9nIkNERLLDyow3+c9/gJgY4/uYxBARkUwxmfEWbCkREZGHYpvJ05lqKSUmMpEhIiKPwMqMJ2M1hoiIvAArM57IVDVGo2EiQ0REHofJjCdJTzdfjVHyt5uIiDwP20yegi0lIiLyUvyvutyZailt28ZEhoiIvAIrM3Kl1QI+Psb3MYkhIiIvwmRGjthSIiIi0mGbSU5WrTKeyAQEMJEhIiKvxcqMXLAaQ0REZBQrM1JnaoBvURETGSIiIjCZka4bN8xXY4KCXBsPERGRRLHNJEVsKREREVmMlRkp2bHDeCKzcSMTGSIiJ9FoBfZduoFNR69h36Ub0Ggd9+9tded25rW9CSszUmEsiWnWDDh61OWhEBF5i20ns5Hy7Wlk55XotqlVAZjXPwm9k9VOPbczr+1tFEJ49n/58/PzoVKpkJeXh7CwMHeHY6hHD+DHHw23e/ZvCxGR2207mY1/rDuMqv/aVv7XcvnwljYnFdWd+7lOiVi5N9Mp1/YU1nx/s83kLgUFFdWYqonMpk1MZIhIVqTeKikr1+Ljny7jlU0n8fFPl1FWroVGK5Dy7WmDZAKAblvKt6dtuhdLzr3qJ8NExhHX9lZsM7nD2LHA6tWG25nEEJHMSL1Vkpp+Gqt+ysTdecHr6WfQt4laL+aqBIDsvBIcyLyJdvdFWnXNA5k3qz23uX/u7bm2t2JlxpVOnKioxlRNZAoLmcgQkexUtlKqfnHn5JXgH+sOY9vJbDdFViE1/TRW7NVPZABAK4Atxy2LLbfAdFLiyGOceR5vwMqMKwgBKI3kjTt3Al27uj4eIiI7VddKUaCiVdIjKQY+ShPTTdx1rgOZN5FbUIKo0AC0TYzQO6a6/caUlWux6qdM62+siqjQAJcc48zzeAMmM872/vvApEn62x56CPj5Z/fEQ0TkAJa0UixplTjriZ9P910xqMhYS62qSJys1TYxAmpVAHLySowmewpUFOnNxWfrtb0V20zOUlJS8ae1aiLz119MZIhI9ixtgZh7X3VtqtT00za3sX6/WWxRfOYMaKautgJkjI9SgXn9kwD87+mkSpWvH24c5ZRreysmM86waxcQGKi/bdmyinbTPfe4JyYiIgeytAVi6n3OfuInPsL+JV82H8u2+Ymi3slqLB/eEjEq/fuPUQXg/aEtcfJavtOu7UpSeZKNbSZHys0FXngB+PTT/21TKACNxvQSBUREMmRJKyXGTKvE2U/8jGiXgNfTz9jVarL3iaLeyWr0SIoxGO9T3b074tquIKUn2ViZcQStFli1CmjUqCKRUSiAf/4TuHWrYh8TGSLyMJa0Uub1TzLZKnHkEz/GqgN+NZR4tmOiQ85vTnWVCR+lAu3ui8TA5rXR7r5I+CgVDmnRuYqp+5Pak2yszNjr+HFgwgRg376K182bAytWAG3bujUsIiJnq2ylVP3feYwF/zt31JM6V64Xo8OinUarA7P7ViRbVeeZUSqAvk3UFj2ebS5OWysT9rboXMXU/c19pDFe++6MQ55kcxQuZ2CrwkIgJQV4552KNlJICPDaaxUDfmswRyQi72HLo9MarUCHRTvNtqkAGN1XKcjPB7fLNNUuCVBWrsWn+67g95vFiI8Iwoh2CfBRKtBqwXb8XXzH5PnDg3yR8XIPo/diz1IIltx7jCoAP8/s5rZBwObuz9Kk4fNnH7SrTWbN9ze/dW319tvAW29V/Prxx4GlS4E6ddwaEhGRLeyd56WylWLt+ef1T8I/1h02+IKsfB3s54OiMo3J896+Y5jIAIbVAb8aSozreK9BTNUxlUbYO8dOdfcOmG/ROZslg7Mt4co2meSTmdTUVHz99dc4e/YsAgMD0b59eyxatAgNGzZ0b2D/+lfFpHczZgCPPOLeWIiIbOTslZ2rO95Um+rpNnF458cLZs9tzwDhA5k3zVZlAOBW8R2jxztijh17WnTOZskAZUu4sk0m+WRmz549mDhxItq0aYPy8nLMmTMHPXv2xOnTpxEcHOy+wEJCgD173Hd9IiI7mWolVA7iNLWyc+X+6lZ2ru78lccbe+Jny/E/HXGLJqsD9gzCddQAXlP37u75ZRxRUakZ5OvSSf8kn8xs27ZN7/Xq1asRFRWFjIwMdOrUyU1RERHJmyWtEnPzvNzdStFohdExKZa2Yoxx9pIA9gzClcsAXls5Im5XD8aVfDJTVV5eHgAgIsJ4xldaWorS0lLd6/x88xMTERF5I0fN8zIl7QjST2QbrEr9SJMYi1ox7+28gLSDV40+MWPPkgDVzXNjzzw59s6xU0lK87Tcrbr7s8TfJlp0ziKreWaEEJg+fTo6dOiA5ORko+9JTU2FSqXS/cTFxbk4SiIi6XPU4Mwtx7ONrkr97fEci45/58cLRucqmbj+CAY0q/hCNzWPzbMdEyuSGhP7zQ2itWeeHHvn2AGkN0/L3Sy5P0u4cgCwrJKZSZMm4fjx4/j8889Nvmf27NnIy8vT/Vy9etWFERIRSc/tMg3mfnMCIz7+DXO/OYHbZRpJt0Aqc6PNx7Lx/lDjSwIsH94Ss/smYfnwlogK9dfbHx3mrzeex9TEb+aWHKhuPJClxxq7tiVPC5lbquFuzlpOwNz9Tete36JzcACwEZMnT8bmzZuxd+9e1DHzCLS/vz/8/f1N7ici8ibPfnIQ20/n6l7/dAH4dH8WujeuBbUqwCFPrThDZRuqZrAffp7ZzeQg2SNZt/BXYanesbkFpTiSdcuip7HsGYRb3bGmrv10mziXrDhuL1P3BwBpB6/a3WZzJMlPmieEwOTJk7Fx40bs3r0b9etblhFWctqkeUREDmTLxHPVqZrIVBUfGYjfb9y26xqWMDWPjCX+/XRzDGxe2+i+1PTTWLE30+SxPZKi8OPpXJsmtrOXIyadM3fv9kza5wiV1weMz5PjiOtb8/0t+TbTxIkTsW7dOqxfvx6hoaHIyclBTk4Obt92/l9AIiJX2HYyGx0W7cSQVfsxJe0ohqzajw6Ldto1buJ2mcZsIgPAJYnM4y1iTbQqGlh0vKlWRVm5Fqt+Mp3IAMB2I4kMYH0rx1qOmnTOnhXHnXVvlexp0TmD5NtMy5cvBwB06dJFb/vq1asxevRo1wdERORAls7FYq2F6acdE6CdHm1RB28+2dxEqyLL5lbFp/uu2LUitqWtHFvYO+mcI1Ycd8Wq21KaJ0fyyYzEu2BERDazd1p8c67cKHZEiHa7WVxmcrkDe6b0//2mY+7PklWxrf2ytuYpHlvuXUqrble3lIWrSD6ZISLyVM78H3ZCZBB+Mr8agEuYe6LFnin94yOCnB6fs1fFnta9AdIOZjltxXEpP7HmaExmiIjcxJn/w36pbxI+3Z9l9XGOZMmU9ra2Kka0S8Dr6WdsbjVV18qxp/1n6aR6k7rVw6Ru9ay+d0dN2udJJD8AmIhICpwxn8c9IZZNIxER5Gf22sZiC/TzQY+kKLPnbVonzKpJ0Kxl6SdU2aoY2Lw22t0XaVFLza+GEs92TDT7nh5JUTZNqmfvAFtrJtWz5d4dMWmfp2FlhoioGk6bz8PCb/vn047g1l0rPFu6qvWqkW1MPp7dIynK7P7ujaNw6s98uwayOntK+9l9K77QV/2UqVehUSoqZgee3TfJ6OdTXStHDqtiS3nVbXeQ/Dwz9uI8M0RkD2fO57Hp6DVMSTtq9XGV1za1qnXV2G6XabAw/TSu3ChGQmQQXuqbhEA/n2rnaWlaJwzH/7BvfTtzc6U4Slm51mChS78a/2s8WDuI19LfF0vuzRnzB7ny/O5kzfc3KzNE5BGc8Y+6I582MvaFGx7ga1Nc1q5qHejng9cebWIQT3XztNibyACWt9Ls4VdDiXEd73XY+Rw5wNbZT/tI5Wkid2MyQ0Sy56w2kKOeNkpNP23QCnk9/QwaRofYHJulq1qbis3eeVos5ubavy1/NjjAVn44AJiIZM2Zqw874mmjylaOsZWlz+QU2hybpUzF5qh5Wqpzvai0+jc5ia1/NjjAVn6YzBCRbFnz1ImxlaPvVlauxcc/XcYrm07i458uo6xca1W7wdjxlrRynC0i0M/odkfN01Idd811Yu8TSVKbrp/MY5uJiGTL0jbQ4BW/4tDvf+u2V64cXflEj6k20LgOidWuLK1WBWDn2f9g2Ef7DY5/ICHCNa0cM87+pwAdG9Yy2O6IeVoUCpg83t2tGEc9kSSV6frJPFZmiEi2LG0D3Z3I3G376Vx0XrzTZBto1U+Z8Kth/ovLr4bCIBGqPH5f5k2L4nOmq7eMt5McMU/Lsx0TbZrHxRUcNSGhLfPAkOsxmSEi2XJEC6O6laPt3W+J+rWC7T6HKebaSbP7JmF8p0RU/X5WKoDxnRKxamQbs62W2X2TJNuK4ZT/3oXzzBCRbGm0Ah0W7TT51IlcfDq2LVonROjNBdO1YTTGrj1o13mVCuDsa3305lwxxt55WqQ410l1fzYq22A/z+zm9ljJOM4zQ+ShpPil4Sqm7t3cystySXCuF5UZzAWz6eg1u8/7bMfEahMZoPp5Wqqby0SKc51U92cD4BNJnoTJDJFMOG1KfRmo7t5NTeteK8QPx6/ZP/Gbs90sNHx82dL2R/+mMfjuRI7J6fy9Gaf89x5sMxHJgDOn1Jc6S+/dWOVm4+E/8MJXx10dstXeeaoZBrWso7fNmjaJRivMtom8nTdXNOWMbSYiD+LIKfWrU93YCWcdCxj/wgFg1b1XbXXUrumauVTsFaMKNNhmTZvER6lw6HT+nkaKbTByLCYzRBLnqCn1q2NqrhVL2hX2HAuYbiM93SbOrnuvnJa+unli7o8NxY9n/jLYZ8nK0WpVAPo1VePjnw1Xbh7XIRFbjmdXe7ypuVjYJiGyDJMZIolz1HwZ5phaPVkroNtuKimx51jAdBspJ68E7/x4waL4Td27j1KBAc3UZleGTq4dhh9P5xrdt+NMrm5lasB0daR3shov9mpktDLVKr6m0furPEd1g1A5cRtR9dhUJZI4Z8+XYcmU+6t+ykRZudahxwKWTTlvCVP3rtEKbD5mfm2mHWdyzV5r87FsvD+0+rlUKp8IenVgMsZ1vFfXYqusrqirHK+2Yi4WTtxGZB4rM0QS5+wVfC1ZPVkrKt5XdVyGPccC1bfQqlPdvVtyfnPxV7axagb74eeZ3WyujrC6QuRcTGaIJM7Z82VYunqysffZcyxgXWvMlnu3p/VW9Tz2DiLlIFQi52GbiUgGnLmCr6WrJxt7nz3HApa3xqZ1b2DTvTtqqnpOeU8kbazMEMmEs1oVlqyerFRUvM+RxwKWt9AmdauHSd3qWX3vlpxfyis/E5FlWJkhkhFnDAS1ZPVkU9Pi23Ms8L8WGlD9ysu23Lsl55fyys9EZBkmM0RU7erJ5h6ttudYwLktNEvOL+WVn4nIMlzOgIh0pDYDsCMrInJc+ZnIm1nz/c1kxgvxH20iIpI6rs1EJnnzystEROSZOGbGi1ROG191ErGcvBL8Y91hbDtpfqZUIiIiKWIy4yUsmTY+5dvT0GgFNFqBfZduYNPRa9h36QY01U3xSkRE5EZsM3kJS1defm/nBaQdvMo2FBERyQaTGQmyd4CusadKLJ3W3dgqxdn/bUNZ+pgqBxgTEZErMZmRGHsH6Kamn8aqnzL1ZjR9Pf0M+jaxr6oiUNGG6pEUYzYx4QBjIiJyNY6ZkRB7B+impp/Gir2ZBlOzawWw5Xg2gvx8DGY5tUZ2XgkOZN40uZ8DjImIyB2YzEiENQN0y8q1+Piny3hl00l8/NNllJVrUVauxaqfMs1e4/YdDQRMT9tuiZy823bHT0RE5EhsM0mEpQN0p6QdQfqJbIM2UtvECLOL/QGAEMATLevgl0vX9a4VowrAQ/dF4qvD16qN82ZRmV3xH8i8iXb3RVZ7HSIiIksxmZEISwfobjlu2KrRCmD/ZdPtn7sF+fvg55ndDAbobj72p0XJTESIv9HtlsZv6fuIiIgsxWTGRo5+4ujeiGAnRvs/8RFBKCvXIv3En7hyoxgJkUFoHheOmLCA6g8GTL4vKtSy4y19HxERkaWYzNjAGU8cueLBZaUC+PXSdbz23Rndtp8uAJ/uz0L3xrWgVgWYbRWpVRVJmzFtEyPsOp6IiMhWHABsJWc9ceSqYbE7zv5ldPuPZ/6CXw3zKdWAZmqT1ScfpQIDmplP5MwdT0REZCsmM1awd0kAS544cqbqBgj/fsP4k0qVNh/LNvk0kkYrsPmY+UTO3PFERES2YpvJCpYvCXARaQezDNpQD913T7UJhZSZexqpus+muuOJiIhsxWTGCpYvCXDeYFtOXgm+OvyHo0NyOVOfAZ9mIiIid5FFm+mDDz5AYmIiAgIC0KpVK/z0009uicOeJ3FkXJDRY+oz4NNMRETkLpJPZr744gtMnToVc+bMwZEjR9CxY0f06dMHWVlZLo+l8okdTx7CaureFLDsaSZbjyciIrKV5JOZJUuWYNy4cXjmmWfQuHFjLF26FHFxcVi+fLnLY/FRKjCvfxIA+5YEsJWzHwRqWicMgOl7m9c/yezTTNV9NuaOJyIispWkk5mysjJkZGSgZ8+eett79uyJX3/91S0x9U5WY/nwlohR6bdLYlQBmNa9vkXn6NdUbZCYKBXA+E6J6JEUZfSYHklRuJz6CAJ9jf+W+ViYI9SpabzN0yMpCpsndTR5b8uHt6x2Dh1zn40lxxMREdlC0gOAr1+/Do1Gg+joaL3t0dHRyMnJMXpMaWkpSktLda/z8/MdHlfvZDV6JMUYzAAMAGkHryInr8ToGBkFKr7Y//10Cyx5qrneDMAj2iXAr0ZFonK7TIOF6ad1M/S+1DcJgX4+AIAzr/VBzt8l6LdsL/JLyhEWUANbJneCKsgXjV/ZVm3s26d1AQCT5zd1b5ZWVOw9noiIyFqSTmYqKRT6X4RCCINtlVJTU5GSkuL0mHyUCqOPGM/rn4R/rDsMBfQH/VZttfgoFRjX8V6j5w7088FrjzYxee2Y8AAcmtvTYHuPpChsP51r8rgeSVG6pMXc+U3dm6XsPZ6IiMgakm4z3XPPPfDx8TGowuTm5hpUayrNnj0beXl5up+rV6+6IlQdd7ZaVo1sY7ZNtWpkG6ddm4iIyF0kXZnx8/NDq1atsH37dgwaNEi3ffv27Rg4cKDRY/z9/eHvb3xlZ1dxZ6tl1cg2ZttUREREnkbSyQwATJ8+HSNGjEDr1q3Rrl07rFy5EllZWZgwYYK7QzPLna2W6tpUREREnkTyyczgwYNx48YNvPrqq8jOzkZycjLS09MRHx/v7tCIiIhIAhRCCE+ZnNao/Px8qFQq5OXlISwszN3hEBERkQWs+f6W9ABgIiIiouowmSEiIiJZYzJDREREssZkhoiIiGSNyQwRERHJGpMZIiIikjUmM0RERCRrkp80z16V0+g4Y/VsIiIico7K721LpsPz+GSmoKAAABAXF+fmSIiIiMhaBQUFUKlUZt/j8TMAa7Va/PnnnwgNDYVC4diFHvPz8xEXF4erV69ydmEr8bOzHT872/Gzsx0/O9vxs7ONEAIFBQWIjY2FUml+VIzHV2aUSiXq1Knj1GuEhYXxD6iN+NnZjp+d7fjZ2Y6fne342VmvuopMJQ4AJiIiIlljMkNERESyxmTGDv7+/pg3bx78/f3dHYrs8LOzHT872/Gzsx0/O9vxs3M+jx8ATERERJ6NlRkiIiKSNSYzREREJGtMZoiIiEjWmMzYYO/evejfvz9iY2OhUCjwzTffuDskWUhNTUWbNm0QGhqKqKgoPProozh37py7w5KN5cuXo2nTprq5Ktq1a4etW7e6OyzZSU1NhUKhwNSpU90diizMnz8fCoVC7ycmJsbdYcnGtWvXMHz4cERGRiIoKAjNmzdHRkaGu8PyOExmbFBUVIRmzZrhvffec3cosrJnzx5MnDgR+/fvx/bt21FeXo6ePXuiqKjI3aHJQp06dfDGG2/g0KFDOHToELp164aBAwfi1KlT7g5NNg4ePIiVK1eiadOm7g5FVu6//35kZ2frfk6cOOHukGTh1q1beOihh+Dr64utW7fi9OnTePvttxEeHu7u0DyOx88A7Ax9+vRBnz593B2G7Gzbtk3v9erVqxEVFYWMjAx06tTJTVHJR//+/fVev/7661i+fDn279+P+++/301RyUdhYSGGDRuGVatWYcGCBe4OR1Zq1KjBaowNFi1ahLi4OKxevVq3LSEhwX0BeTBWZsht8vLyAAARERFujkR+NBoN0tLSUFRUhHbt2rk7HFmYOHEiHnnkEXTv3t3docjOhQsXEBsbi8TERDz99NO4fPmyu0OShc2bN6N169Z48sknERUVhRYtWmDVqlXuDssjMZkhtxBCYPr06ejQoQOSk5PdHY5snDhxAiEhIfD398eECROwceNGJCUluTssyUtLS0NGRgZSU1PdHYrsPPDAA/jkk0/w/fffY9WqVcjJyUH79u1x48YNd4cmeZcvX8by5ctRv359fP/995gwYQKef/55fPLJJ+4OzeOwzURuMWnSJBw/fhw///yzu0ORlYYNG+Lo0aP4+++/sWHDBowaNQp79uxhQmPG1atXMWXKFPzwww8ICAhwdziyc3dLvUmTJmjXrh3uu+8+rF27FtOnT3djZNKn1WrRunVrLFy4EADQokULnDp1CsuXL8fIkSPdHJ1nYWWGXG7y5MnYvHkzdu3a5fQVzT2Nn58f6tWrh9atWyM1NRXNmjXDv//9b3eHJWkZGRnIzc1Fq1atUKNGDdSoUQN79uzBu+++ixo1akCj0bg7RFkJDg5GkyZNcOHCBXeHInlqtdrgPxqNGzdGVlaWmyLyXKzMkMsIITB58mRs3LgRu3fvRmJiortDkj0hBEpLS90dhqQ9/PDDBk/fjBkzBo0aNcLMmTPh4+PjpsjkqbS0FGfOnEHHjh3dHYrkPfTQQwbTT5w/fx7x8fFuishzMZmxQWFhIS5evKh7nZmZiaNHjyIiIgJ169Z1Y2TSNnHiRKxfvx6bNm1CaGgocnJyAAAqlQqBgYFujk76XnrpJfTp0wdxcXEoKChAWloadu/ebfCUGOkLDQ01GJcVHByMyMhIjteywAsvvID+/fujbt26yM3NxYIFC5Cfn49Ro0a5OzTJmzZtGtq3b4+FCxfiqaeewoEDB7By5UqsXLnS3aF5HkFW27VrlwBg8DNq1Ch3hyZpxj4zAGL16tXuDk0Wxo4dK+Lj44Wfn5+oVauWePjhh8UPP/zg7rBkqXPnzmLKlCnuDkMWBg8eLNRqtfD19RWxsbHiscceE6dOnXJ3WLLx7bffiuTkZOHv7y8aNWokVq5c6e6QPBJXzSYiIiJZ4wBgIiIikjUmM0RERCRrTGaIiIhI1pjMEBERkawxmSEiIiJZYzJDREREssZkhoiIiGSNyQwRERHJGpMZIpKU3bt3Q6FQ4O+//672vWvWrEF4eLjTY7JUQkICli5d6u4wiLwOkxkicgqpJRqO5Mn3RiRHTGaIiIhI1pjMEJFRXbp0waRJkzBp0iSEh4cjMjISL7/8MiqXcysrK8OMGTNQu3ZtBAcH44EHHsDu3bsBVLSKxowZg7y8PCgUCigUCsyfPx8AsG7dOrRu3RqhoaGIiYnB0KFDkZub67C4v/32W7Rq1QoBAQG49957kZKSgvLyct1+hUKBjz76CIMGDUJQUBDq16+PzZs3651j8+bNqF+/PgIDA9G1a1esXbtW1/oyd28AUFxcjLFjxyI0NBR169blCslEruDmhS6JSKI6d+4sQkJCxJQpU8TZs2fFunXrRFBQkG7V36FDh4r27duLvXv3iosXL4rFixcLf39/cf78eVFaWiqWLl0qwsLCRHZ2tsjOzhYFBQVCCCE+/vhjkZ6eLi5duiT27dsnHnzwQdGnTx/ddStXpb9161a1Ma5evVqoVCrd623btomwsDCxZs0acenSJfHDDz+IhIQEMX/+fN17AIg6deqI9evXiwsXLojnn39ehISEiBs3bgghhMjMzBS+vr7ihRdeEGfPnhWff/65qF27ti4mc/cWHx8vIiIixPvvvy8uXLggUlNThVKpFGfOnLH3t4OIzGAyQ0RGde7cWTRu3FhotVrdtpkzZ4rGjRuLixcvCoVCIa5du6Z3zMMPPyxmz54thDBMNEw5cOCAAKBLCOxJZjp27CgWLlyo955PP/1UqNVq3WsA4uWXX9a9LiwsFAqFQmzdulV3j8nJyXrnmDNnjl5Mpu4tPj5eDB8+XPdaq9WKqKgosXz58mrvhYhsV8ONRSEikrgHH3wQCoVC97pdu3Z4++23cejQIQgh0KBBA733l5aWIjIy0uw5jxw5gvnz5+Po0aO4efMmtFotACArKwtJSUl2xZuRkYGDBw/i9ddf123TaDQoKSlBcXExgoKCAABNmzbV7Q8ODkZoaKiu1XXu3Dm0adNG77xt27a1OIa7z61QKBATE+PQNhoRGWIyQ0Q28fHxQUZGBnx8fPS2h4SEmDymqKgIPXv2RM+ePbFu3TrUqlULWVlZ6NWrF8rKyuyOSavVIiUlBY899pjBvoCAAN2vfX199fYpFApdUiWE0EvgKrdZyty5icg5mMwQkUn79+83eF2/fn20aNECGo0Gubm56Nixo9Fj/fz8oNFo9LadPXsW169fxxtvvIG4uDgAwKFDhxwWb8uWLXHu3DnUq1fP5nM0atQI6enpetuqxmjs3ojIffg0ExGZdPXqVUyfPh3nzp3D559/jmXLlmHKlClo0KABhg0bhpEjR+Lrr79GZmYmDh48iEWLFukSgYSEBBQWFmLHjh24fv06iouLUbduXfj5+WHZsmW4fPkyNm/ejNdee81h8b7yyiv45JNPMH/+fJw6dQpnzpzBF198gZdfftnic4wfPx5nz57FzJkzcf78efzf//0f1qxZAwC6io2xeyMi92EyQ0QmjRw5Erdv30bbtm0xceJETJ48Gc899xwAYPXq1Rg5ciT+9a9/oWHDhhgwYAB+++03XcWlffv2mDBhAgYPHoxatWrhzTffRK1atbBmzRp8+eWXSEpKwhtvvIG33nrLYfH26tULW7Zswfbt29GmTRs8+OCDWLJkCeLj4y0+R2JiIr766it8/fXXaNq0KZYvX445c+YAAPz9/U3eGxG5j0JY0wwmIq/RpUsXNG/enNPzA3j99dfx4Ycf4urVq+4OhYiM4JgZIqIqPvjgA7Rp0waRkZH45ZdfsHjxYkyaNMndYRGRCWwzEZFk9enTByEhIUZ/Fi5c6LTrXrhwAQMHDkRSUhJee+01/Otf/9Kb5ZeIpIVtJiKSrGvXruH27dtG90VERCAiIsLFERGRFDGZISIiIlljm4mIiIhkjckMERERyRqTGSIiIpI1JjNEREQka0xmiIiISNaYzBAREZGsMZkhIiIiWWMyQ0RERLL2/wEB9nO3oOfgYgAAAABJRU5ErkJggg==\n", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.plot(x_train,y_train,'o',label='dataset entraînement')\n", "plt.plot(x_train, model(X_train, theta), c='r',label='modèle initial')\n", "plt.xlabel('petal_length')\n", "plt.ylabel('petal_width')\n", "plt.legend()\n", "plt.show()"]}, {"attachments": {"Capture%20d'%C3%A9cran%202023-04-04%20113116.png": {"image/png": "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"}}, "cell_type": "markdown", "id": "4f8ef408", "metadata": {}, "source": ["## 3. Fonction coût : <PERSON><PERSON><PERSON> Quadratique <PERSON>\n", "On mesure les erreurs du modele sur le Dataset X, y en implémenterl'erreur quadratique moyenne, <PERSON> (MSE) en anglais.\n", "![Capture%20d'%C3%A9cran%202023-04-04%20113116.png](attachment:Capture%20d'%C3%A9cran%202023-04-04%20113116.png)\n", "\n", "Ensuite, on teste notre fonction, pour voir s'il n'y a pas de bug"]}, {"cell_type": "code", "execution_count": 113, "id": "3bfb4285", "metadata": {}, "outputs": [], "source": ["def cost_function(X, y, theta):\n", "    m = len(y)\n", "    return 1/(2*m) * np.sum((model(X, theta) - y)**2)"]}, {"cell_type": "code", "execution_count": 114, "id": "ec4ef451", "metadata": {}, "outputs": [{"data": {"text/plain": ["19.572463404066532"]}, "execution_count": 114, "metadata": {}, "output_type": "execute_result"}], "source": ["cost_function(X_train, y_train, theta)"]}, {"attachments": {"Capture%20d'%C3%A9cran%202023-04-04%20114408.png": {"image/png": "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"}}, "cell_type": "markdown", "id": "527094e1", "metadata": {}, "source": ["![Capture%20d'%C3%A9cran%202023-04-04%20114408.png](attachment:Capture%20d'%C3%A9cran%202023-04-04%20114408.png)"]}, {"cell_type": "code", "execution_count": 115, "id": "bc48ccbb", "metadata": {}, "outputs": [], "source": ["def grad(X, y, theta):\n", "    m = len(y)\n", "    return 1/m * X.T.dot(model(X, theta) - y)"]}, {"cell_type": "code", "execution_count": 116, "id": "dcda7267", "metadata": {}, "outputs": [], "source": ["def gradient_descent(X, y, theta, learning_rate, n_iterations):\n", "    \n", "    cost_history = np.zeros(n_iterations) # création d'un tableau de stockage pour enregistrer l'évolution du Cout du modele\n", "    \n", "    for i in range(0, n_iterations):\n", "        theta = theta - learning_rate * grad(X, y, theta) # mise a jour du parametre theta (formule du gradient descent)\n", "        cost_history[i] = cost_function(X, y, theta) # on enregistre la valeur du Cout au tour i dans cost_history[i]\n", "        \n", "    return theta, cost_history"]}, {"cell_type": "markdown", "id": "42add5a3", "metadata": {}, "source": ["## 5. Phase d'entrainement\n", "On définit un nombre d'itérations, ainsi qu'un pas d'apprentissage α. Une fois le modele entrainé, on observe les resultats par rapport a notre Dataset"]}, {"cell_type": "code", "execution_count": 117, "id": "1d0edf28", "metadata": {}, "outputs": [{"data": {"text/plain": ["array([13.24853318,  8.97183163,  6.07960893,  4.12366741,  2.80090458,\n", "        1.9063414 ,  1.30135673,  0.89220531,  0.61549005,  0.42833743,\n", "        0.30175336,  0.21613003,  0.15820728,  0.11901765,  0.09249673,\n", "        0.07454333,  0.06238395,  0.05414296,  0.04855191,  0.04475299,\n", "        0.04216611,  0.04039894,  0.03918619,  0.03834844,  0.03776433,\n", "        0.03735183,  0.03705544,  0.03683763,  0.03667301,  0.03654444,\n", "        0.03644029,  0.03635273,  0.03627643,  0.03620782,  0.03614446,\n", "        0.03608471,  0.03602746,  0.03597195,  0.03591769,  0.03586432,\n", "        0.03581161,  0.03575941,  0.03570761,  0.03565613,  0.03560493,\n", "        0.03555397,  0.03550323,  0.0354527 ,  0.03540236,  0.0353522 ,\n", "        0.03530223,  0.03525244,  0.03520282,  0.03515337,  0.0351041 ,\n", "        0.03505499,  0.03500605,  0.03495728,  0.03490868,  0.03486025,\n", "        0.03481198,  0.03476387,  0.03471593,  0.03466816,  0.03462055,\n", "        0.0345731 ,  0.03452581,  0.03447869,  0.03443172,  0.03438492,\n", "        0.03433828,  0.0342918 ,  0.03424547,  0.03419931,  0.0341533 ,\n", "        0.03410745,  0.03406176,  0.03401622,  0.03397084,  0.03392561,\n", "        0.03388054,  0.03383562,  0.03379086,  0.03374625,  0.03370179,\n", "        0.03365749,  0.03361333,  0.03356933,  0.03352548,  0.03348177,\n", "        0.03343822,  0.03339482,  0.03335156,  0.03330845,  0.03326549,\n", "        0.03322268,  0.03318002,  0.03313749,  0.03309512,  0.03305289,\n", "        0.0330108 ,  0.03296886,  0.03292706,  0.03288541,  0.0328439 ,\n", "        0.03280253,  0.0327613 ,  0.03272021,  0.03267926,  0.03263845,\n", "        0.03259779,  0.03255726,  0.03251687,  0.03247662,  0.0324365 ,\n", "        0.03239653,  0.03235669,  0.03231698,  0.03227741,  0.03223798,\n", "        0.03219868,  0.03215952,  0.03212049,  0.0320816 ,  0.03204283,\n", "        0.0320042 ,  0.03196571,  0.03192734,  0.0318891 ,  0.031851  ,\n", "        0.03181303,  0.03177518,  0.03173747,  0.03169988,  0.03166243,\n", "        0.0316251 ,  0.0315879 ,  0.03155082,  0.03151388,  0.03147706,\n", "        0.03144036,  0.03140379,  0.03136735,  0.03133103,  0.03129483,\n", "        0.03125876,  0.03122282,  0.03118699,  0.03115129,  0.03111571,\n", "        0.03108025,  0.03104491,  0.0310097 ,  0.0309746 ,  0.03093963,\n", "        0.03090477,  0.03087004,  0.03083542,  0.03080092,  0.03076654,\n", "        0.03073227,  0.03069813,  0.0306641 ,  0.03063018,  0.03059639,\n", "        0.03056271,  0.03052914,  0.03049569,  0.03046235,  0.03042913,\n", "        0.03039602,  0.03036302,  0.03033014,  0.03029737,  0.03026471,\n", "        0.03023216,  0.03019973,  0.0301674 ,  0.03013519,  0.03010309,\n", "        0.03007109,  0.03003921,  0.03000743,  0.02997577,  0.02994421,\n", "        0.02991276,  0.02988141,  0.02985018,  0.02981905,  0.02978803,\n", "        0.02975711,  0.0297263 ,  0.0296956 ,  0.029665  ,  0.0296345 ,\n", "        0.02960411,  0.02957382,  0.02954364,  0.02951356,  0.02948358,\n", "        0.02945371,  0.02942394,  0.02939427,  0.0293647 ,  0.02933523,\n", "        0.02930586,  0.0292766 ,  0.02924743,  0.02921836,  0.0291894 ,\n", "        0.02916053,  0.02913176,  0.02910309,  0.02907452,  0.02904604,\n", "        0.02901766,  0.02898938,  0.0289612 ,  0.02893311,  0.02890512,\n", "        0.02887723,  0.02884943,  0.02882172,  0.02879411,  0.0287666 ,\n", "        0.02873917,  0.02871185,  0.02868461,  0.02865747,  0.02863042,\n", "        0.02860347,  0.0285766 ,  0.02854983,  0.02852315,  0.02849656,\n", "        0.02847007,  0.02844366,  0.02841734,  0.02839111,  0.02836498,\n", "        0.02833893,  0.02831297,  0.0282871 ,  0.02826132,  0.02823563,\n", "        0.02821002,  0.02818451,  0.02815908,  0.02813373,  0.02810848,\n", "        0.02808331,  0.02805822,  0.02803322,  0.02800831,  0.02798348,\n", "        0.02795874,  0.02793408,  0.02790951,  0.02788502,  0.02786062,\n", "        0.02783629,  0.02781205,  0.0277879 ,  0.02776383,  0.02773983,\n", "        0.02771593,  0.0276921 ,  0.02766835,  0.02764469,  0.02762111,\n", "        0.0275976 ,  0.02757418,  0.02755084,  0.02752758,  0.02750439,\n", "        0.02748129,  0.02745827,  0.02743532,  0.02741245,  0.02738966,\n", "        0.02736695,  0.02734432,  0.02732176,  0.02729929,  0.02727688,\n", "        0.02725456,  0.02723231,  0.02721014,  0.02718804,  0.02716602,\n", "        0.02714407,  0.0271222 ,  0.02710041,  0.02707869,  0.02705704,\n", "        0.02703547,  0.02701397,  0.02699254,  0.02697119,  0.02694991,\n", "        0.0269287 ,  0.02690757,  0.02688651,  0.02686552,  0.0268446 ,\n", "        0.02682376,  0.02680298,  0.02678228,  0.02676164,  0.02674108,\n", "        0.02672059,  0.02670017,  0.02667982,  0.02665953,  0.02663932,\n", "        0.02661918,  0.0265991 ,  0.0265791 ,  0.02655916,  0.02653929,\n", "        0.02651949,  0.02649975,  0.02648009,  0.02646049,  0.02644096,\n", "        0.02642149,  0.02640209,  0.02638276,  0.02636349,  0.02634429,\n", "        0.02632516,  0.02630609,  0.02628709,  0.02626815,  0.02624927,\n", "        0.02623046,  0.02621172,  0.02619304,  0.02617442,  0.02615587,\n", "        0.02613738,  0.02611895,  0.02610059,  0.02608229,  0.02606405,\n", "        0.02604587,  0.02602776,  0.02600971,  0.02599172,  0.02597379,\n", "        0.02595592,  0.02593812,  0.02592037,  0.02590269,  0.02588506,\n", "        0.0258675 ,  0.02585   ,  0.02583255,  0.02581517,  0.02579785,\n", "        0.02578058,  0.02576337,  0.02574623,  0.02572914,  0.02571211,\n", "        0.02569514,  0.02567822,  0.02566137,  0.02564457,  0.02562783,\n", "        0.02561115,  0.02559452,  0.02557795,  0.02556144,  0.02554498,\n", "        0.02552858,  0.02551224,  0.02549595,  0.02547972,  0.02546354,\n", "        0.02544742,  0.02543135,  0.02541534,  0.02539939,  0.02538348,\n", "        0.02536764,  0.02535184,  0.0253361 ,  0.02532042,  0.02530479,\n", "        0.02528921,  0.02527368,  0.02525821,  0.02524279,  0.02522743,\n", "        0.02521211,  0.02519685,  0.02518164,  0.02516649,  0.02515138,\n", "        0.02513633,  0.02512133,  0.02510638,  0.02509148,  0.02507663,\n", "        0.02506183,  0.02504708,  0.02503239,  0.02501774,  0.02500314,\n", "        0.0249886 ,  0.0249741 ,  0.02495965,  0.02494526,  0.02493091,\n", "        0.02491661,  0.02490236,  0.02488816,  0.02487401,  0.0248599 ,\n", "        0.02484584,  0.02483184,  0.02481788,  0.02480396,  0.0247901 ,\n", "        0.02477628,  0.02476251,  0.02474879,  0.02473511,  0.02472148,\n", "        0.0247079 ,  0.02469436,  0.02468087,  0.02466743,  0.02465403,\n", "        0.02464068,  0.02462738,  0.02461411,  0.0246009 ,  0.02458773,\n", "        0.0245746 ,  0.02456152,  0.02454849,  0.0245355 ,  0.02452255,\n", "        0.02450965,  0.02449679,  0.02448398,  0.02447121,  0.02445848,\n", "        0.0244458 ,  0.02443316,  0.02442056,  0.02440801,  0.0243955 ,\n", "        0.02438303,  0.02437061,  0.02435822,  0.02434588,  0.02433359,\n", "        0.02432133,  0.02430912,  0.02429695,  0.02428482,  0.02427273,\n", "        0.02426068,  0.02424867,  0.02423671,  0.02422478,  0.0242129 ,\n", "        0.02420106,  0.02418926,  0.02417749,  0.02416577,  0.02415409,\n", "        0.02414245,  0.02413085,  0.02411929,  0.02410776,  0.02409628,\n", "        0.02408484,  0.02407343,  0.02406207,  0.02405074,  0.02403945,\n", "        0.0240282 ,  0.02401699,  0.02400582,  0.02399469,  0.02398359,\n", "        0.02397253,  0.02396151,  0.02395053,  0.02393958,  0.02392868,\n", "        0.0239178 ,  0.02390697,  0.02389618,  0.02388542,  0.02387469,\n", "        0.02386401,  0.02385336,  0.02384275,  0.02383217,  0.02382163,\n", "        0.02381113,  0.02380066,  0.02379023,  0.02377983,  0.02376947,\n", "        0.02375914,  0.02374885,  0.0237386 ,  0.02372838,  0.02371819,\n", "        0.02370804,  0.02369793,  0.02368785,  0.0236778 ,  0.02366779,\n", "        0.02365781,  0.02364787,  0.02363796,  0.02362808,  0.02361824,\n", "        0.02360843,  0.02359865,  0.02358891,  0.02357921,  0.02356953,\n", "        0.02355989,  0.02355028,  0.0235407 ,  0.02353116,  0.02352165,\n", "        0.02351217,  0.02350273,  0.02349332,  0.02348393,  0.02347459,\n", "        0.02346527,  0.02345598,  0.02344673,  0.02343751,  0.02342832,\n", "        0.02341916,  0.02341003,  0.02340094,  0.02339187,  0.02338284,\n", "        0.02337384,  0.02336486,  0.02335592,  0.02334701,  0.02333813,\n", "        0.02332928,  0.02332046,  0.02331167,  0.02330291,  0.02329418,\n", "        0.02328548,  0.02327681,  0.02326817,  0.02325956,  0.02325098,\n", "        0.02324243,  0.02323391,  0.02322541,  0.02321695,  0.02320851,\n", "        0.02320011,  0.02319173,  0.02318338,  0.02317506,  0.02316677,\n", "        0.0231585 ,  0.02315027,  0.02314206,  0.02313388,  0.02312573,\n", "        0.02311761,  0.02310951,  0.02310144,  0.0230934 ,  0.02308539,\n", "        0.02307741,  0.02306945,  0.02306152,  0.02305361,  0.02304574,\n", "        0.02303789,  0.02303006,  0.02302227,  0.0230145 ,  0.02300676,\n", "        0.02299904,  0.02299135,  0.02298369,  0.02297605,  0.02296844,\n", "        0.02296085,  0.02295329,  0.02294576,  0.02293825,  0.02293077,\n", "        0.02292331,  0.02291588,  0.02290848,  0.0229011 ,  0.02289374,\n", "        0.02288641,  0.02287911,  0.02287183,  0.02286457,  0.02285734,\n", "        0.02285014,  0.02284296,  0.0228358 ,  0.02282867,  0.02282156,\n", "        0.02281448,  0.02280742,  0.02280039,  0.02279338,  0.02278639,\n", "        0.02277943,  0.02277249,  0.02276558,  0.02275868,  0.02275182,\n", "        0.02274497,  0.02273815,  0.02273135,  0.02272458,  0.02271783,\n", "        0.0227111 ,  0.0227044 ,  0.02269771,  0.02269106,  0.02268442,\n", "        0.02267781,  0.02267122,  0.02266465,  0.0226581 ,  0.02265158,\n", "        0.02264508,  0.0226386 ,  0.02263214,  0.02262571,  0.02261929,\n", "        0.0226129 ,  0.02260653,  0.02260019,  0.02259386,  0.02258756,\n", "        0.02258128,  0.02257501,  0.02256878,  0.02256256,  0.02255636,\n", "        0.02255019,  0.02254403,  0.0225379 ,  0.02253179,  0.02252569,\n", "        0.02251962,  0.02251357,  0.02250754,  0.02250154,  0.02249555,\n", "        0.02248958,  0.02248363,  0.02247771,  0.0224718 ,  0.02246591,\n", "        0.02246005,  0.0224542 ,  0.02244838,  0.02244257,  0.02243678,\n", "        0.02243102,  0.02242527,  0.02241954,  0.02241384,  0.02240815,\n", "        0.02240248,  0.02239683,  0.0223912 ,  0.02238559,  0.02238   ,\n", "        0.02237443,  0.02236888,  0.02236334,  0.02235783,  0.02235233,\n", "        0.02234685,  0.02234139,  0.02233595,  0.02233053,  0.02232513,\n", "        0.02231975,  0.02231438,  0.02230903,  0.0223037 ,  0.02229839,\n", "        0.0222931 ,  0.02228782,  0.02228257,  0.02227733,  0.02227211,\n", "        0.0222669 ,  0.02226172,  0.02225655,  0.0222514 ,  0.02224627,\n", "        0.02224116,  0.02223606,  0.02223098,  0.02222592,  0.02222087,\n", "        0.02221584,  0.02221083,  0.02220584,  0.02220086,  0.0221959 ,\n", "        0.02219096,  0.02218604,  0.02218113,  0.02217624,  0.02217136,\n", "        0.0221665 ,  0.02216166,  0.02215684,  0.02215203,  0.02214724,\n", "        0.02214246,  0.0221377 ,  0.02213296,  0.02212823,  0.02212352,\n", "        0.02211883,  0.02211415,  0.02210948,  0.02210484,  0.02210021,\n", "        0.02209559,  0.02209099,  0.02208641,  0.02208184,  0.02207729,\n", "        0.02207275,  0.02206823,  0.02206373,  0.02205924,  0.02205476,\n", "        0.0220503 ,  0.02204586,  0.02204143,  0.02203702,  0.02203262,\n", "        0.02202823,  0.02202386,  0.02201951,  0.02201517,  0.02201085,\n", "        0.02200654,  0.02200224,  0.02199797,  0.0219937 ,  0.02198945,\n", "        0.02198521,  0.02198099,  0.02197679,  0.02197259,  0.02196841,\n", "        0.02196425,  0.0219601 ,  0.02195596,  0.02195184,  0.02194774,\n", "        0.02194364,  0.02193956,  0.0219355 ,  0.02193145,  0.02192741,\n", "        0.02192339,  0.02191938,  0.02191538,  0.0219114 ,  0.02190743,\n", "        0.02190347,  0.02189953,  0.0218956 ,  0.02189169,  0.02188779,\n", "        0.0218839 ,  0.02188002,  0.02187616,  0.02187231,  0.02186848,\n", "        0.02186466,  0.02186085,  0.02185705,  0.02185327,  0.0218495 ,\n", "        0.02184574,  0.021842  ,  0.02183826,  0.02183455,  0.02183084,\n", "        0.02182715,  0.02182347,  0.0218198 ,  0.02181614,  0.0218125 ,\n", "        0.02180887,  0.02180525,  0.02180164,  0.02179805,  0.02179447,\n", "        0.0217909 ,  0.02178734,  0.0217838 ,  0.02178027,  0.02177675,\n", "        0.02177324,  0.02176974,  0.02176626,  0.02176279,  0.02175933,\n", "        0.02175588,  0.02175244,  0.02174901,  0.0217456 ,  0.0217422 ,\n", "        0.02173881,  0.02173543,  0.02173206,  0.02172871,  0.02172537,\n", "        0.02172203,  0.02171871,  0.0217154 ,  0.0217121 ,  0.02170882,\n", "        0.02170554,  0.02170228,  0.02169902,  0.02169578,  0.02169255,\n", "        0.02168933,  0.02168612,  0.02168292,  0.02167973,  0.02167656,\n", "        0.02167339,  0.02167024,  0.02166709,  0.02166396,  0.02166084,\n", "        0.02165773,  0.02165462,  0.02165153,  0.02164845,  0.02164539,\n", "        0.02164233,  0.02163928,  0.02163624,  0.02163321,  0.0216302 ,\n", "        0.02162719,  0.02162419,  0.02162121,  0.02161823,  0.02161526,\n", "        0.02161231,  0.02160936,  0.02160643,  0.0216035 ,  0.02160058,\n", "        0.02159768,  0.02159478,  0.0215919 ,  0.02158902,  0.02158616,\n", "        0.0215833 ,  0.02158045,  0.02157762,  0.02157479,  0.02157197,\n", "        0.02156916,  0.02156637,  0.02156358,  0.0215608 ,  0.02155803,\n", "        0.02155527,  0.02155252,  0.02154978,  0.02154705,  0.02154432,\n", "        0.02154161,  0.02153891,  0.02153621,  0.02153353,  0.02153085,\n", "        0.02152818,  0.02152553,  0.02152288,  0.02152024,  0.02151761,\n", "        0.02151498,  0.02151237,  0.02150977,  0.02150717,  0.02150459,\n", "        0.02150201,  0.02149944,  0.02149688,  0.02149433,  0.02149179,\n", "        0.02148926,  0.02148673,  0.02148421,  0.02148171,  0.02147921,\n", "        0.02147672,  0.02147424,  0.02147176,  0.0214693 ,  0.02146684,\n", "        0.02146439,  0.02146195,  0.02145952,  0.0214571 ,  0.02145468,\n", "        0.02145228,  0.02144988,  0.02144749,  0.02144511,  0.02144273,\n", "        0.02144037,  0.02143801,  0.02143566,  0.02143332,  0.02143099,\n", "        0.02142866,  0.02142634,  0.02142404,  0.02142173,  0.02141944,\n", "        0.02141715,  0.02141488,  0.02141261,  0.02141034,  0.02140809,\n", "        0.02140584,  0.0214036 ,  0.02140137,  0.02139915,  0.02139693,\n", "        0.02139472,  0.02139252,  0.02139033,  0.02138814,  0.02138596,\n", "        0.02138379,  0.02138163,  0.02137947,  0.02137732,  0.02137518,\n", "        0.02137304,  0.02137092,  0.0213688 ,  0.02136668,  0.02136458,\n", "        0.02136248,  0.02136039,  0.02135831,  0.02135623,  0.02135416,\n", "        0.0213521 ,  0.02135004,  0.02134799,  0.02134595,  0.02134392,\n", "        0.02134189,  0.02133987,  0.02133786,  0.02133585,  0.02133385,\n", "        0.02133186,  0.02132987,  0.02132789,  0.02132592,  0.02132395,\n", "        0.02132199,  0.02132004,  0.02131809,  0.02131615,  0.02131422])"]}, "execution_count": 117, "metadata": {}, "output_type": "execute_result"}], "source": ["n_iterations = 1000\n", "learning_rate = 0.01\n", "\n", "\n", "theta_final, cost_history = gradient_descent(X_train, y_train, theta, learning_rate, n_iterations)\n", "cost_history # l'historique de la fonction coût qu'on va afficher par la suite. on remarque que sa valeur déminue et c'est ça l'objectif"]}, {"cell_type": "code", "execution_count": 118, "id": "71ae7b0a", "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[ 0.39646625],\n", "       [-0.28177528]])"]}, "execution_count": 118, "metadata": {}, "output_type": "execute_result"}], "source": ["theta_final # voici les parametres du modele une fois que la machine a été entrainée"]}, {"cell_type": "code", "execution_count": 119, "id": "61edd405", "metadata": {}, "outputs": [{"data": {"text/plain": ["array([0.39646625])"]}, "execution_count": 119, "metadata": {}, "output_type": "execute_result"}], "source": ["theta_final[0]"]}, {"cell_type": "code", "execution_count": 120, "id": "5a4314c9", "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.plot(x_train,y_train,'o',label='dataset entraînement')\n", "plt.plot(x_train, model(X_train, theta_final), c='r',label='modèle final')\n", "plt.xlabel('petal_length')\n", "plt.ylabel('petal_width')\n", "plt.legend()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 121, "id": "606bfc53", "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# création d'un vecteur prédictions qui contient les prédictions de notre modele final\n", "predictions = model(X_test, theta_final)\n", "\n", "# Affichage des résultats de prédictions (en rouge) par rapport a notre Dataset (en bleu)\n", "plt.plot(x_test,y_test,'o',label='dataset test')\n", "plt.plot(x_test,predictions , c='r',label='prédictions')\n", "plt.xlabel('petal_length')\n", "plt.ylabel('petal_width')\n", "plt.legend()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 122, "id": "415ab0a8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[0.70939036]\n"]}], "source": ["# Test: # prédire quelle serait la largeur d'un pétale qui a une longueur de 2,5 cm\n", "X_2_5= np.array([2.5,1])\n", "pred_y_2_5=model(X_2_5, theta_final) \n", "print(pred_y_2_5)"]}, {"cell_type": "markdown", "id": "3d087de6", "metadata": {}, "source": ["## 6. <PERSON><PERSON><PERSON> d'apprentissage\n", "\n", "   Pour vérifier si notre algorithme de Descente de gradient a bien fonctionné, on observe l'évolution de la fonction cout a travers les itérations. On est sensé obtenir une courbe qui diminue a chaque itération jusqu'a stagner a un niveau minimal (proche de zéro). Si la courbe ne suit pas ce motif, alors le pas learning_rate est peut-etre trop élevé, il faut prendre un pas plus faible."]}, {"cell_type": "code", "execution_count": 123, "id": "519d3dbd", "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.plot(range(n_iterations), cost_history)\n", "plt.show()"]}, {"cell_type": "markdown", "id": "eb5cab7c", "metadata": {}, "source": ["## 7. Evaluation du modèle - Coefficient de détermination\n", "\n", "   Le coefficient de détermination est un indice de la qualité de la prédiction de la régression linéaire. Le coefficient de détermination se situe entre 0 et 1. Plus il est proche de 1, plus la régression linéaire est en adéquation avec les données collectées"]}, {"attachments": {"Capture%20d'%C3%A9cran%202023-04-04%20123236.png": {"image/png": "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"}}, "cell_type": "markdown", "id": "cc10bb18", "metadata": {}, "source": ["![Capture%20d'%C3%A9cran%202023-04-04%20123236.png](attachment:Capture%20d'%C3%A9cran%202023-04-04%20123236.png)"]}, {"cell_type": "code", "execution_count": 124, "id": "ea8f0458", "metadata": {}, "outputs": [], "source": ["def coef_determination(y, pred):\n", "    u = ((y - pred)**2).sum()\n", "    v = ((y - y.mean())**2).sum()\n", "    return 1 - u/v"]}, {"cell_type": "code", "execution_count": 125, "id": "406b1dfe", "metadata": {}, "outputs": [{"data": {"text/plain": ["0.9251697212034784"]}, "execution_count": 125, "metadata": {}, "output_type": "execute_result"}], "source": ["coef_determination(y_test, predictions) # predictions=model(X_test,theta_final)"]}, {"cell_type": "code", "execution_count": null, "id": "84d1f8e1", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "2c926f5b", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.13"}}, "nbformat": 4, "nbformat_minor": 5}