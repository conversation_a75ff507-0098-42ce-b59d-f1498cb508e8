{"cells": [{"cell_type": "markdown", "id": "946b14c2", "metadata": {}, "source": ["  # <PERSON><PERSON><PERSON> (x1, x2) - <PERSON>rad<PERSON> Descent"]}, {"cell_type": "code", "execution_count": 2, "id": "9678a1cc", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "from sklearn.datasets import make_regression\n", "import matplotlib.pyplot as plt\n", "%matplotlib inline"]}, {"attachments": {"Capture%20d'%C3%A9cran%202023-04-11%20133257.png": {"image/png": "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"}}, "cell_type": "markdown", "id": "cb6250d0", "metadata": {}, "source": ["\n", "# 1. Dataset\n", "\n", "   Pour développer un modèle de régression multivariée ou multiple à partir des équations de la régression linéaire , il suffit d'ajouter des colonnes à la matrice X ainsi qu'un nombre égal de lignes dans le vecteur θ. Dans cet exemple, nous aurons deux variables x1,x2. Donc les matrices seront sous les formes suivantes:![Capture%20d'%C3%A9cran%202023-04-11%20133257.png](attachment:Capture%20d'%C3%A9cran%202023-04-11%20133257.png)\n", "   "]}, {"cell_type": "code", "execution_count": 3, "id": "f0d53f36", "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["np.random.seed(0) # permet de reproduire l'aléatoire\n", "\n", "x, y = make_regression(n_samples=100, n_features=2, noise = 10) # creation d'un dataset (x, y) linéaire\n", "\n", "plt.scatter(x[:,0], y)# afficher les résultats. x_1 en abscisse et y en ordonnée\n", "plt.xlabel('x1')\n", "plt.ylabel('y')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 4, "id": "ac391645", "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.scatter(x[:,1], y)# afficher les résultats. x_2 en abscisse et y en ordonnée\n", "plt.xlabel('x2')\n", "plt.ylabel('y')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 5, "id": "1244eb40", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(100, 2)\n", "(100,)\n", "(100, 1)\n"]}], "source": ["# Verification des dimensions\n", "print(x.shape)\n", "print(y.shape)\n", "\n", "# redimensionner y\n", "y = y.reshape(y.shape[0], 1)\n", "print(y.shape)"]}, {"cell_type": "code", "execution_count": 6, "id": "a498c557", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(100, 3)\n", "[[ 1.05445173 -1.07075262  1.        ]\n", " [-0.36274117 -0.63432209  1.        ]\n", " [-0.85409574  0.3130677   1.        ]\n", " [ 1.3263859   0.29823817  1.        ]\n", " [-0.4615846  -1.31590741  1.        ]\n", " [ 1.94362119 -1.17312341  1.        ]\n", " [-1.60205766  0.62523145  1.        ]\n", " [-0.40178094  0.17742614  1.        ]\n", " [-0.97727788  1.86755799  1.        ]\n", " [ 0.37816252  0.15494743  1.        ]]\n"]}], "source": ["# Création de la matrice X, inclut le Biais\n", "X = np.hstack((x, np.ones((x.shape[0], 1)))) # ajoute un vecteur Biais de dimension (x.shape[0], 1)\n", "\n", "print(X.shape)\n", "print(X[:10])"]}, {"cell_type": "code", "execution_count": 7, "id": "da27ffa1", "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[-0.82646112],\n", "       [ 0.78420863],\n", "       [-0.1954172 ]])"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["# Initialisation du vecteur theta aléatoire, avec 3 éléments (car X a trois colonnes)\n", "theta = np.random.randn(3 , 1)\n", "theta"]}, {"cell_type": "markdown", "id": "b62a3e72", "metadata": {}, "source": ["## 2. <PERSON><PERSON><PERSON><PERSON>\n", "On implémente un modèle selon l'équation matricielle F=X.θ et puis on teste le modèle initiale défini par la valeur initiale de θ qu'on a initialisé d'une manière aléatoire."]}, {"cell_type": "code", "execution_count": 8, "id": "45512217", "metadata": {}, "outputs": [], "source": ["def model(X, theta):\n", "    return X.dot(theta)"]}, {"attachments": {"Capture%20d'%C3%A9cran%202023-04-04%20113116.png": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAANoAAAArCAYAAAAe05ZnAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAAEnQAABJ0Ad5mH3gAAAzzSURBVHhe7Z1xaBvXGcC/bX8YOjD0D4cNIlanaTvsNsPeolYhOArFdVgcm1qZt0l1QCXp7LqLnITMWdqpC63dtLXjLLObpUpCa9ckdsuK1C5IJcWGBolipIArBVrLpbHMOiRokSBFgpa37909ySfpTtJdZMXE7wfnu3unO53e+77vfe9738M/IghwOJxV5cdsz+FwVhGuaBxOBeCKxuFUAK5oHE4F4IrG4VQArmgcTgXgisbhVACuaOUiEQbP2x4Ip9g5hyOBK9rt8tkEWA48DZZOC/Sc8MIyV7R1QgpCbx8Dyz4LPPHoDtj7l2kIsytycEW7XR7pgknHRTjd28gKOOuB1LVheHZ+G5wcn4QP/tUJ8B4q3Ukfqp88q6hoKQhf7gPL6z5IsJLipCDwTwv0XQ4rvvB6IHalDzZt2iRsO0wWwWoW3HY9mvn8ynYMPN+yB8qQWpyGPssw+Ap8Zn0TAdcRCwxfk5PeBPg856Eaj6icVjV0wr7fYru96QafkrDTXMfSCZJRfS2prc3d9MQ+E2efEVly2YjeOkWW2HkeUS9xHDQRU0czaWrpJ+4IK8c7pqx6YnMp3rkmibq6sR7sJKcaNELrQKxb/WGnch1mkSTxSJC4z9lIM2ujfrfCy0ScxKa3kqmv2HkWQeIwStuWbfoh4v8eL9+aIfbca9j+o/Pi3WqIeh3E1oEy0NJEmv/qLvF3VpBbXjKgN5HRQJIVpImTmZeaSK2xmzhZHfrP0HpoJY6QeJ6LSkVbIXihVahk/Rk/K5GADdlda1L8UrGhUZn+Tas2Sbyv4EsedePrM0IOYqpVEoS1SXkVDRHqSBRk6yWVIhidIUMdeK+soVsizt5aYroQZOdKoMLtFr+/ObeNo/huqFzmU26y8A0rU4lgiPU24qQGNokCjd+jaBjuIPGrdnzPAeK9xQpkYYZxN35OoT40Kho2VrfYCAOf5Go7VRx9tuJIueUno1QIep0kyopEIbURd7oA73QfRSV+YUb+GWuQsisaIggj1nEtGq3R+dx6LoJCr5X8ZACfiR5ECQqy9K5VaGPam3kzX49tf1hPTKh8Kt8oQzIwioa0lnS7MhIgKH/tYXdGJtYOosEpZJiSc0OkFdvIflXZIGoboyUWwe+hBy3w8OYqoSjDTSc43ozB/jaj4MPmEro8CMPX6+H4s21Qw8pEfBD+HzvEO41t+yE2OQ7um6xoHaLb8yqcfobWUgCG/34eAt+J5SWxsQ2eP70TUv+NsQJKBJwXz0PsmXYw3suKCqB7/PeAw3wcfIzBzBwdjdAx9CEYr34VRv7cCDktXyIhmHh5GAJ1x+G5PdkSANfCEGWHa4d6aHnKAIFzOP6SCxwsu6D/aAh2TV+EE4/rWGE+2hTtCz9M0P32ndDwM6EkQ2TODbPQBdu2yDTDtx6s5ABAXTsY6lgZEl3GMkCB+F48p1Rt2YZPmQWnL8JK1iNVYDh4Fo404OH1Yeg5oxzVkqPG0AVdBokw3/SBexaga2t9aUpyrxHanxEPz7s8EHi/Hw59tQ9GXjSCskgVJuGZgMHrKL5tBhThNFGUG9yhCKzFIJhuy06ozxgbCcuz8OJJL2wbOwu9v6mG8MfTEFAILmlStNA8thZSs3VzToXH4IYPr9XpoEamO4t8PAXTuK/vkFZyDJbD1Opi40tbv7oGdKiMvtCiiqjlHSA2C4P7LNA3Ro2FGwZ7LGAZUacQBbmnEfa/cATo5EHszT7o/0C74YmFvGi66kH3czlfQ44qaNzRK3oe7/XB3ncegJGX2jQrGe1R3VOCBED79hUJgNgyLFARqKvS2EuqBBVk8IAF9u7aAU8c9+BbrRCbHYSe585DSNqAGzcL9e/7QjJT9l0AxmyDkNreDrp4AHw+F0yPhCDxE3Y9Bw2KFoHFuZBw1N4gqSyBCITfx91DOtggFkiIgO+KqKDGLdL7IhCk98D9oMvyJDaA7iHcfRTKqog1R40Rjo9PwqT7U/jyy0/ho0k8PmQoq8BUNfTCyKARj2LgQrfLtSyWqyWy6MK/ufVcmCrDLjjAvA/D79qg8R7xWBOsR0UJgEaJRwM3g0DfTF5uykwqhAoyDrpjkzDypzoIX7aD6zN2DSVtBl1rz5U4pHKM/gass1BomXa6SAJmX+/BIVAYpo+jYbXQrQ/OowmS62Ao6hWt0PgskRB97PtqcsZfCN4XEvUMxjql8z17YYwW1j0MG7Nuwmfch7tYFGJFujQ6eSg7v1RwwzHPWvRTFND94QScfhIrKOaCPhtaXFZeOglICI3zANSoUDRYXoSgKF3ge8ej4XtXSIRD2KNSxmCvdM6vU5AAqK/fmC83ZSYxOwHjjx2AzvsT6H1RQW5cMTyxG+C9hnvLr2GzWMJAo78Vd59H2BiyGowvUsP6Zfb24X6Jp5YDC4qUztyQGInqkgkdf03D+nhNLuQ/PypG0CTRRko6stV6Lj+qI85NdBPn16xgvfO1OxPyN70RVBn1Y5G92lEi0zryRGaIvcNERt91ECttOzpfFmDXNBB8Qy+8+0q0kZKeM1SegyJkgUwdNBNzV+nbUF40nHErTuL00ldT4m+SRMdpRJa+nzlvOkVD3eWgWtEy82fY0HkUULTof2zCfa1ZYdLClXynFY2+72puWhDD86hoqsPrKoVFmIZJJw6I0y30nfWveTWG9aPEfZg+I6et0wK/20GKzeyVk6VLZuH3SOfuREMgN/l++4pWwHWMwezJHnSxemDiBitCHzY0y8Znj2R3rgL3VCv72D+IfprhQcl9Nzwwgb5EjeUIdEp99iw2QHWRcYFm1/EH9gAF8lyDMm/qSUHoegDgydMawutVUF1CSF8kAq6/HQL/7ovw6h4a+hCnWyixs06Y1Zi2lRJEwAD19wunAiHPBLqTNdB1qFPZ7So7EfB95MN9C2z7VXpQhbLtoz5yOzQ+KJbkcTvBGqZw+YQcpJVaGtTwofRsZcb6oGbLmjWWoiU38chczhXXg1o4tCAd+CzZWfe0BdRuRcpLkgTf6hfckmZ9EzEdnUKHprIIE9iK9VUc0WJLEwPkiKMnYcrvMZNeMsTc1mzXT0qcLAW8JKhw2X8qpz2ZK3w7k9/akOmhovguVLZzhjYiTK5lr5WGco/202q0M5uh6+yHcMSAepwIwNjhYzBbY4AjL+2HRlnV1kH9E7jLDBolPLINenHQGVymVoMmHA+A/ZoBTv+jVyGSFYXI57h7MncK4c6gNlu73KSuj8GhlwGeV6yv4uge3IV/FyHCghu5pL4NgevE07B3BNv42Zwes8oAO58SvRHP+zNo//OJvNcnJEHvaR2Wndytf4xOFQRhmSYmfBeG6dft4NuupXe+XVC2f0EjIBGICb1zAnxvOYTIp2F7Q35AJhGDKNZZDXbFmoM1TOFkic85iK2lmZioFW8xke5Xpoi/iEqLvq98KlIygINqfJ65o5k0H3QQf6E0oLiYvGp9VzmtpXLEycwLOL487GS9WNoiljflShEhnUouuVUl6JGYsU7tV7NfOj5jZ6leks0oGTNJejPFzyDJeQcx4+eajiolCCeJ/5wVZclMTCgHtnP+TCCi4twKkvGDNKHdTMxmK7HStEClYI/g3elXPDsNaMx1LABzL/NzINUhRoDWSmKx+mztspEVlFCDmHOanajLgk+vaA1o3CUk4yQuhB7TsATqrJzOFYTOQ+FaqZRf0QTrXyCpuCRYUvGaFYjsbO0l1wCx9ZrQko8T54UBMnBmiPR3NBHrW24yc8ZO7KfE825htYIaaAJvk6YlQ/EZGp3M73GFbPQSk4rvSjK980p0UawTJWMmKqHqFRQ5rIKiIUKvVmCZTDGEZTLYe2TWqK0tsrK1acOZHcR9ic4HWoljXpRsocfT43lINBXCece4igAKullyQYmixMmCyy5kx9fKrn4QjUTxZTJ3KSzood8/ToK0YtlyJNNrXtmOQVDC3UOaA1BpVkfRkKILPxWhgrCGF37ShjFayehculmS6IZEiRvHcLWZ3yuO4VaW+eBv6lI3ySzUHxWGb6ibU2SLBInX6yZTp+zEKlm0mTsWy6CwhGa9sHR1QIwVdNGFx/3E4VUIPCgu/FTPqikaFcCFSzZiVrAU8lArbia2SwsqrXiFoJkSvf1kivVSC1enWEDHT4ZQsDPZLbmBHCEIQRuMKkbxX5acF9drZQUeVG+FAzXJ8BSxmXHcsV5dyKJQt51mmJQuvYVYRUW7yxCCEs2k/5IXew+6OcnAbibMQlSqlQx4xUYRAzkrrrOQTYO93cK8g1hVGR7O3YKmZTLrj8LZ2olIGEINBtj5SzHLIHzDAzWPd4KRZbvoNjWCLuqEwYkUHPijQXZBLOfuhv8jQg6nAvAejcOpAFzROJwKwBWNw6kAXNE4nArAFY3DqQBc0TicCsAVjcOpAFzROJwKwBWNw1l1AP4PYPd1bJtRxssAAAAASUVORK5CYII="}}, "cell_type": "markdown", "id": "580ecc60", "metadata": {}, "source": ["## 3. Fonction coût : <PERSON><PERSON><PERSON> Quadratique <PERSON>\n", "On mesure les erreurs du modele sur le Dataset X, y en implémenterl'erreur quadratique moyenne, Mean Squared Error (MSE) en anglais.![Capture%20d'%C3%A9cran%202023-04-04%20113116.png](attachment:Capture%20d'%C3%A9cran%202023-04-04%20113116.png)\n"]}, {"cell_type": "code", "execution_count": 9, "id": "733261f5", "metadata": {}, "outputs": [], "source": ["def cost_function(X, y, theta):\n", "    m = len(y)\n", "    return 1/(2*m) * np.sum((model(X, theta) - y)**2)"]}, {"cell_type": "code", "execution_count": 10, "id": "f7bdf186", "metadata": {}, "outputs": [{"data": {"text/plain": ["5294.071342926028"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["cost_function(X, y, theta)"]}, {"attachments": {"Capture%20d'%C3%A9cran%202023-04-04%20113945.png": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAOQAAAAtCAYAAAC+sMfxAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAAEnQAABJ0Ad5mH3gAAA7kSURBVHhe7Z1/TJvXuce/u7kSVf9w7q6u0SphdQ50vcXtFlzVXKMscZSbQVsCXWDuhgcdFsnCYIPESoO8LNwuCU3TeCSZU5SYoRbCOmjWXjtTZXpX4d4h+1a5OFvLy6TE9KaYKZF9lQpPibCU3XOf9wdgwBDbhOQleT+S9fr88Ms55z3PeZ7znHNevsQIKCgoyIK/k64KCgoyQBFIBQUZoQikgoKMUARSQUFGKAKpoCAjFIFUUJAR91wg46MeeD6NS6HkxD7th29MCigo3MdkLpA3Q/C83oyWI51oa7LA+ssAYlJSItxbFlhqKrFp3TqsK/wWLI2d4CT5i188BWt7BLm5WWKEQByhcy2w2ulT0oKBa4AqNw/cy5XoHJWyKCjcr/AbA9JnivmPGtjWN0ak8CTz7tWyijcvS+F5TA6yA1otK33VTzklpkaYc3stc3FTUoTIuKeJGWpdbOQGY8O/0DIt/YbPMRV0stLnKV7MpqBwX5Khhgwj/Hke9LdiiAphFfTfLEbwrA+cEJ7HGAcv1NA/o6OcIjFfDxyPvIDy/ATt+IUPnYfGUG41Q/cwhf+ePhdC9NeArIIyVP9jG9xDS5u38iGG0EAPBi6tlvIqyIEMBTIP5hMdqHsqDPeZTvQHopi6RdFjYUST2K3cRS8Jrg66vBlxRHCoHyZTAYnpLCGPEz3Qo0jH54sieoUu8TgZsTwa6IxqdA4FpbBc4dBTY4WVzHRLfSv84btc2msDaK3hpwlLfNoDMm/DB5fMBHLCh1azFT1/1cO8sw7GvxzAJtsAkJ8NVeJ0UCCKsSDpzYIi6B6VohAC16uG5p/WSmEeDr53gsAzT0P3ZT4cQWiILgV5JIoi2Y8agSGOfi1ndKju7kLX0UYaWu4+0Qv/Di73RdhP9KK3+zhq1AEErhfB5uLDvbAbY4itVWHBY1KQBekLZJzDqSYrggU22LblCSaoZsOzKKarukCHvPlPOsZh+H3qpmSuTgsWrk1gBNnQZE9rTGJiDMOjdK+rfWjmR/GKH8FB9rA+L2fGzFU/8hgwSlr4phShMI8oAgNxmL9fJg5q8RCG35v7XLJzcqB/dOZJpEV8rB/NFgcCX0gRCvMIw2OzwDGUzL2ZGmkLZIxMTcdFHYxbZueDuB7BBBmfRmNC3DRJ5o/JiF8ZwQBpF/P+LmEk79hJ2hAmmDfkiRkS4c3j+5kvBtDCe6Xnf+o9wnwal3pQuSC9GQN/CSPy8Asw5gp3oXxBuOmiN8y2/eSNbOi+utSTWIQJD/ZVeVHUZoNRsGAS4dC5eX556FPoQPBvlHyTLKr5aesKcepT8dfpEA10ormiEpUlm/At+4DYHrJBg7KDjYjbrDh1McNJgeTcSZnhk1qm1e5i7qtSBDFyupRptx9m/usUmBph3Xur2K63RY/ryK8obV5+FvGyJm0pc3FSmIh4dlG+fczL30Py2pbuH2QRIVUi6KQ8B9jgjKtWxlx1s11aLTvw+2UU9rqX7aN7aOnT5JnTEkJbbNVuZPveHGaRuY7qGcbfrqLfVsxp58wYZ+4GLav41e183CPM9bxY3q0nh6U4iYibnrmBVf3Cyy4Lzzh9BA+8oYm5wxSY8rPD9Hf2eeXXGSZ/f4DKSfJwQ4pIg7Q1ZF5+Nem7tciaNk1p5HS9rYb9oDRyXvLB+VEUWbyH9GYQ3nc56F96EcavCLlF1Go8RqNqODI7iqhzae5IM5usNaQt/9sF51g1bD80zXH6RK9epnmqBuoMBvhVyZdNKK8XW8Dz3uCsNqB2PXWoG/knzuLIS3qok04Ioxi9EKD20kOXI0VlSHyoB4ffN2PHt3VSzGLoUPwDk/AtdHYQgZnHS6bckcOY2N2Brt3FyFugYW8Pv2a9u8kD/f6fooyvT1YennwO6P8gQDWVF6ot1FbqTjh+k3TNYUnSFkjVFju6WqfgsrXAcaQFlpNhPHv6OOqmly+eKsNr26jFQm60NDkRK+tC+/65gsV7aXUWekz/G5HCRH45bC9xcNqbUd/zEGwnXoFpXkeKXBuDbkPCXPS+JwvG53dQNyd8bgQ+579Q5/7Zbgw/30XtvERLzMwfiyA4rTMmDHdXJ6I7y2FKQZA0W16Emf8SPYXBC7xExhH85W50q15D+4/1GTqTOPQcIvM3347GbXN7EoZCSOhFMoEGpu8bETztTRiUUkTSlHedSe8+pm1wzzVJl2Sc9VWXssN/WA32KnEnTFYBvt6iGVj6hp/5T1awCjIHF7FSZ+FcrJR+U/vOuBSRIVf6WG1a9Zhi/lfF8mr3utnwu01s4x431SJzhL7C1/90osk8wpwG/u842TzjWB5I7X/4D7d9UnPIbNnjDqAymWG74oI7xe1w8Yse9GdVw7xB7vZqFL5DFlhsTgQp5H29HpYaBwIZe4Y1MJaJZiD3ugXNV2pS0jThTwZJr+hQRCb+cohyfvjoPppHUm33LOg3NYgW0blmVJ59DO0Hy5Zh1YTh7eunqw7lZB3NEJ3AZd5WJcssM62bJhM+tO2wJHUmRX1tqE/YEiqQkycsewUupblIJwnmPYHfDlexPYXtcDeGmbNq4Ta7B4YE587SI+4kGzxaxaqqq1iFic9vYFurKFznYsMZNp3oxGti3tRNGWLWuVP19p3R0Frtsbma8MIx4f7aPd40rKwMkbZ5dofIXnmXdz4amPMTKW3GgplXvmkNnmb57vlLrvjTHu6/mWB+avERWDjtsaYcZYnb7FImjkC7Fc6LUjBVNtjQtTPTOc+dhXdoWCpoDsUHKjvwx6PFSy4h3Tli8P1sPay9Npz7rCH1jQ4THjRXNMMjaDA7zv+uTpwHZ0Dsw1as39EjhRai238e562Z3j01YgMt2PpJOf5zrw6+l9ej/lwxjvs7UMY7KqMDaC6sh8fShT8eNCU8lyg8jYVk0aRX/5QFkl87kiOfffaZ9O3us9w2SaXs8dFO1DeMoOSH2XDYO+kxJ3SGFUfqVO+nIZD8Lq4mJ7K/V4Lhl9vI3FXD9tuP0VAgpacJ11GIba9TrU98jI4Zh04Y/dZNaPHpYP/dedTlS9FzCKG/qRXuNDYx6Hd2wbYhyRB8M4bYGhVU1/ph3dwCX8KgGB9qwxM1nTC2fYTe7yYa5hm0HQ8vkAoyJexmTYYK5gzy9uasGbhsR03KRIT1x5QdJ/zUYruBNXn48olryXx5DUfFEzvpE2HePfw95q5Zz5ixd/n0j7iuO3ftc+QNA8UlmrDTpNl2EvfMqXP34E3WhI3VqX7OCAbivYNfa2zqxtq2dtIu/KgtutJ5fG8OIP0VrkzIgirlNcP5yzEqmMrqhJRohxu+DLfbxQVHiRG66d1HBDfQI2je6t3mjE3h9Akj8EGArsUo+sa0YRoGF+Dt8nLovybGLCBdp5MkmCvHjcvMfbSJ7XvVxQ7/pIrVnkw4E8lz3c+cdZS+t4pVnU7Bnf8gcGucufdslDRNAjMODhqRg1LccpgcZ8P+ERa5JYWTIGqA2zl1JtlwsuWYKT87JixNaNmu+TuNZphk40E/G1kkWTgTm6hlrnrJatCmtvRzR0mi8YQdZxSXdPlOcuqktbRH00fpukLc5iCzYOKUsgNe6nhknu3S1rK+K2LSg8pUmB+gSAjq+pKs3VGn+InYwQ3Swe3MGWd9tdK9ljAp+W1gC0zGBKaujzD3v1VQnmPMn0Swh09uFf6GtjZZfagU79SK6Qb6fZJCTPmPMcP01ksa3Pv2GJhhmeuamSH25cTtnf6jfL0X8SRLh/INM30/NVbYZF36IDP3mzY41lajuphMHH6rHRki3P+ku7Xh/iD0VqXgJHpiowWOD6m1PmzBppcHZl+L8nk/rOsK0XxeDEbPWPAEv0k7MU9aZEP3L0Yy/DTYrMtZ1KxS5enIYOQQvjr3r8R8rSjky/v0NjS/xZv3p2D5185ZUzoegKNwHSrbpXU4H9WHL+/mhDxE9uNFMKoBzaYnkZOkEFnGRnS0xOH8gQWV238E/+Md+A/HctY1MyULxoYuvLItiLbv0ZTG0gzXf/H1VqPo8SSlmQghSGnm9UkORyyFJJgrx61Jdnmwj7lOu1ifPyKt4xxggxM0gvCmx/SG5U+cNBLeiZ0tCncWSZMuWyOvcqYm2eRkYgtITrZFNLvgAFokbSlWVkMucZB5zaeD6InqsLlAHEGi4cukRdPZESIjeAeMvR7WzaTdzvXDccSBzkNWbCppg8fXj9ZXpLDlFAKZH5W7R2hQYqmG+kzmjplVD6/tN67H+vUbZ46MxT7sh2tUjbL9ZhgXaHYOA2cDMO1OlrY0KyeQtznIjD/30Jc4/O1W0av5iofCeuQt82TCvSD8vgsR47PQ3QzA96e1MO+1oa7uBeRf6kT3R9moa5XCgW54g6tOIqHaUgebiTrge3fHtys7YjGEaRah3tIIE+9NnfDggL0Hmvrj+HmSDf6CsKIBjWXpG9YrJpBLH2T+B1zld84804jj3fyrJeww0zxCt60E+lWoIDXPvQb7VyPwkcY3PmeEZg1FXg3THEKPku+YhPlOPDSCAbURRf+8CitINTD//DhyTjvQL5w4ecBQF8PmqoMu3IO2HZWobPLjyeMf49xe42zfnoYGZaedQ83BRuj5F7WlyYoJZGiU3+6Ugye1s0XmhtzgCsrxovH/EB0iAfx6LoknMRqAe9SE8u36hRVcDTysQiQ0TIaKDk9LAie82GvmLGIcwaFOqMlCyFfFEOdP0a82csrw2q9L4Lc/mK/w0PDHDr0fkPI4h3O/PYI63hO1AH4t1oksR5e0dpw+KyaQSx9kFs9Dip5VqsQbLqj321Cd0V5VORDDWJDmxiY98oWF9DA4Hzd7FjEWgPeMDuXfzsXoq81wT/B5Vh9ZuWYc7032Cg8FEQ3KHL2wLeNE0gpuLo+De2sfWj96CEayu4PXNaipq0bx18TCxkmD1jcMQvV14KFv2mCvXPqdO/KGf6eMFf6GXnRV5onvxHnaBVVvF+xGvlYceixWuLM2Q/dctVDX1Tr0KKwsyr80V1CQESu77KGgoJAWikAqKMgIRSAVFGSEIpAKCjJCEUgFBRmhCKSCgoxQBFJBQUYoAqmgICMUgVRQkA3A/wMviufek5fwDwAAAABJRU5ErkJggg=="}}, "cell_type": "markdown", "id": "b0e44329", "metadata": {}, "source": ["## 4. Grad<PERSON> et Descente de Gradient\n", "On implémente la formule du gradient pour la MSE. Ensuite on utilise cette fonction dans la descente de gradient:\n", "![Capture%20d'%C3%A9cran%202023-04-04%20113945.png](attachment:Capture%20d'%C3%A9cran%202023-04-04%20113945.png)"]}, {"attachments": {"Capture%20d'%C3%A9cran%202023-04-04%20114408.png": {"image/png": "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"}}, "cell_type": "markdown", "id": "04b45aa9", "metadata": {}, "source": ["![Capture%20d'%C3%A9cran%202023-04-04%20114408.png](attachment:Capture%20d'%C3%A9cran%202023-04-04%20114408.png)"]}, {"cell_type": "code", "execution_count": 11, "id": "dcc8cb74", "metadata": {}, "outputs": [], "source": ["def grad(X, y, theta):\n", "    m = len(y)\n", "    return 1/m * X.T.dot(model(X, theta) - y)"]}, {"cell_type": "code", "execution_count": 12, "id": "f2067baa", "metadata": {}, "outputs": [], "source": ["def gradient_descent(X, y, theta, learning_rate, n_iterations):\n", "    \n", "    cost_history = np.zeros(n_iterations) # création d'un tableau de stockage pour enregistrer l'évolution du Cout du modele\n", "    \n", "    for i in range(0, n_iterations):\n", "        theta = theta - learning_rate * grad(X, y, theta) # mise a jour du parametre theta (formule du gradient descent)\n", "        cost_history[i] = cost_function(X, y, theta) # on enregistre la valeur du Cout au tour i dans cost_history[i]\n", "        \n", "    return theta, cost_history"]}, {"cell_type": "markdown", "id": "8574b884", "metadata": {}, "source": ["## 5. Phase d'entrainement\n", "On définit un nombre d'itérations, ainsi qu'un pas d'apprentissage α. Une fois le modele entrainé, on observe les resultats par rapport a notre Dataset"]}, {"cell_type": "code", "execution_count": 13, "id": "a2077aba", "metadata": {}, "outputs": [], "source": ["n_iterations = 1000\n", "learning_rate = 0.01\n", "\n", "theta_final, cost_history = gradient_descent(X, y, theta, learning_rate, n_iterations)"]}, {"cell_type": "code", "execution_count": 14, "id": "c3e1c119", "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[28.67154616],\n", "       [97.29524926],\n", "       [-0.511481  ]])"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["# création d'un vecteur prédictions qui contient les prédictions de notre modele final\n", "predictions = model(X, theta_final)\n", "\n", "theta_final"]}, {"cell_type": "code", "execution_count": 15, "id": "efa17c1b", "metadata": {}, "outputs": [{"data": {"text/plain": ["Text(0.5, 0, 'y')"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["#%matplotlib notebook\n", "fig = plt.figure()\n", "ax = fig.add_subplot(111, projection='3d')\n", "ax.scatter(x[:,0], x[:,1], y)\n", "ax.scatter(x[:,0], x[:,1], predictions)\n", "# affiche les noms des axes\n", "ax.set_xlabel('x_1')\n", "ax.set_ylabel('x_2')\n", "ax.set_zlabel('y')"]}, {"cell_type": "markdown", "id": "823e6ce8", "metadata": {}, "source": ["## 6. <PERSON><PERSON><PERSON> d'apprentissage\n", "\n", "   Pour vérifier si notre algorithme de Descente de gradient a bien fonctionné, on observe l'évolution de la fonction cout a travers les itérations. On est sensé obtenir une courbe qui diminue a chaque itération jusqu'a stagner a un niveau minimal (proche de zéro). Si la courbe ne suit pas ce motif, alors le pas learning_rate est peut-etre trop élevé, il faut prendre un pas plus faible."]}, {"cell_type": "code", "execution_count": 16, "id": "5facda20", "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAjEAAAGdCAYAAADjWSL8AAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjUuMiwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy8qNh9FAAAACXBIWXMAAA9hAAAPYQGoP6dpAAA1/ElEQVR4nO3de3xU9b3/+/dkkkwuJItcSIZIQFBENHgDG4K20HIRK1K3v1NbsSn+6kNrVTRb3bbUfX6lPZbwsGeru5tW0Xqkrdj09FRat782BaulpYRbNJWLgFaEgLlwSSYJJJPLfM8fIUuHIJLMZc0kr+fjMQ+StT4z+a4vaN6P72UtlzHGCAAAIM4kON0AAACAwSDEAACAuESIAQAAcYkQAwAA4hIhBgAAxCVCDAAAiEuEGAAAEJcIMQAAIC4lOt2ASAkEAvrwww+VkZEhl8vldHMAAMA5MMaotbVVBQUFSkg4+1jLkA0xH374oQoLC51uBgAAGITa2lqNGTPmrDVDNsRkZGRI6u2EzMxMh1sDAADORUtLiwoLC+3f42czZENM3xRSZmYmIQYAgDhzLktBWNgLAADiEiEGAADEJUIMAACIS4QYAAAQlwgxAAAgLhFiAABAXCLEAACAuESIAQAAcYkQAwAA4hIhBgAAxCVCDAAAiEuEGAAAEJeG7AMgI2VfQ6v+3221yhnh0bdmXeB0cwAAGLYYiRmgOl+HfrZxv175x4dONwUAgGGNEDNAVmqSJKmlvcvhlgAAMLwRYgaoL8T4CDEAADiKEDNAI0+FmDZ/t7p6Ag63BgCA4YsQM0CZp0KMxJQSAABOIsQMkDvBpYyU3k1dzYQYAAAcQ4gZBNbFAADgPELMIIxMOxViThJiAABwyoBCzLJly+RyuYJeXq/XPm+M0bJly1RQUKDU1FTNmjVLu3btCvoMv9+vJUuWKDc3V+np6Vq4cKEOHToUVNPU1KTS0lJZliXLslRaWqrm5ubBX2WYMRIDAIDzBjwSc+mll6qurs5+7dixwz73+OOP64knntDKlSu1bds2eb1ezZ07V62trXZNWVmZ1q5dq4qKCm3cuFFtbW1asGCBenp67JpFixappqZGlZWVqqysVE1NjUpLS0O81PDpCzHNJzsdbgkAAMPXgB87kJiYGDT60scYo6eeekqPPvqobr75ZknSz3/+c+Xn5+ull17SN7/5Tfl8Pj3//PP65S9/qTlz5kiSXnzxRRUWFuq1117Tddddp3feeUeVlZXavHmziouLJUnPPfecSkpKtHfvXk2aNCmU6w0LKzVZkuRr73a4JQAADF8DHol59913VVBQoPHjx+urX/2q3n//fUnS/v37VV9fr3nz5tm1Ho9HM2fO1KZNmyRJ1dXV6urqCqopKChQUVGRXVNVVSXLsuwAI0nTp0+XZVl2jdOYTgIAwHkDGokpLi7WL37xC1100UVqaGjQY489phkzZmjXrl2qr6+XJOXn5we9Jz8/XwcOHJAk1dfXKzk5WVlZWf1q+t5fX1+vvLy8fj87Ly/PrjkTv98vv99vf9/S0jKQSxuQvoW9ze1MJwEA4JQBhZjrr7/e/nrKlCkqKSnRBRdcoJ///OeaPn26JMnlcgW9xxjT79jpTq85U/2nfU55ebm+//3vn9N1hIrnJwEA4LyQtlinp6drypQpevfdd+11MqePljQ2NtqjM16vV52dnWpqajprTUNDQ7+fdeTIkX6jPB+3dOlS+Xw++***************************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\n", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.plot(range(n_iterations), cost_history)\n", "plt.show()"]}, {"cell_type": "markdown", "id": "176b11d1", "metadata": {}, "source": ["## 7. Evaluation du modèle - Coefficient de détermination\n", "\n", "   Le coefficient de détermination est un indice de la qualité de la prédiction de la régression polynomiale. Le coefficient de détermination se sit!ue entre 0 et 1. Plus il est proche de 1, plus la régression polynomiale est en adéquation avec les données collectées."]}, {"attachments": {"Capture%20d'%C3%A9cran%202023-04-04%20123236.png": {"image/png": "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"}}, "cell_type": "markdown", "id": "2792a73e", "metadata": {}, "source": ["![Capture%20d'%C3%A9cran%202023-04-04%20123236.png](attachment:Capture%20d'%C3%A9cran%202023-04-04%20123236.png)"]}, {"cell_type": "code", "execution_count": 17, "id": "4dc559b8", "metadata": {}, "outputs": [], "source": ["def coef_determination(y, pred):\n", "    u = ((y - pred)**2).sum()\n", "    v = ((y - y.mean())**2).sum()\n", "    return 1 - u/v"]}, {"cell_type": "code", "execution_count": 18, "id": "56333445", "metadata": {}, "outputs": [{"data": {"text/plain": ["0.9916687122277607"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["coef_determination(y, predictions)"]}, {"cell_type": "code", "execution_count": null, "id": "e18ad6c7", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.13"}}, "nbformat": 4, "nbformat_minor": 5}