{"cells": [{"cell_type": "code", "execution_count": 1, "id": "00961034", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "import pandas as pd\n", "\n", "%matplotlib inline"]}, {"cell_type": "markdown", "id": "1eea030e", "metadata": {}, "source": ["### 1. Dataset"]}, {"cell_type": "code", "execution_count": 2, "id": "a3a91f62", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>x1</th>\n", "      <th>x2</th>\n", "      <th>label</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>0.448196</td>\n", "      <td>0.130705</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>0.773273</td>\n", "      <td>0.086142</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>0.418814</td>\n", "      <td>0.634072</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>0.951985</td>\n", "      <td>0.403000</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>0.628209</td>\n", "      <td>0.040618</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>195</th>\n", "      <td>0.213643</td>\n", "      <td>0.266799</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>196</th>\n", "      <td>0.853088</td>\n", "      <td>0.248827</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>197</th>\n", "      <td>0.818051</td>\n", "      <td>0.490837</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>198</th>\n", "      <td>0.761045</td>\n", "      <td>0.977934</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>199</th>\n", "      <td>0.906128</td>\n", "      <td>0.950444</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>200 rows × 3 columns</p>\n", "</div>"], "text/plain": ["           x1        x2  label\n", "0    0.448196  0.130705    0.0\n", "1    0.773273  0.086142    0.0\n", "2    0.418814  0.634072    1.0\n", "3    0.951985  0.403000    0.0\n", "4    0.628209  0.040618    0.0\n", "..        ...       ...    ...\n", "195  0.213643  0.266799    1.0\n", "196  0.853088  0.248827    0.0\n", "197  0.818051  0.490837    0.0\n", "198  0.761045  0.977934    1.0\n", "199  0.906128  0.950444    0.0\n", "\n", "[200 rows x 3 columns]"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["dataset = pd.read_csv(\"classification.csv\")\n", "dataset"]}, {"cell_type": "code", "execution_count": 3, "id": "61522175", "metadata": {}, "outputs": [], "source": ["X = dataset[['x1', 'x2']].values\n", "y = dataset[['label']].values"]}, {"cell_type": "code", "execution_count": 4, "id": "c0fb5232", "metadata": {"scrolled": true}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 640x480 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["dataset.plot.scatter(x='x1',y='x2', c='label', colormap='winter')\n", "plt.show()"]}, {"cell_type": "markdown", "id": "e3dc2093", "metadata": {}, "source": ["### 2. <PERSON><PERSON><PERSON><PERSON>\n"]}, {"cell_type": "code", "execution_count": 5, "id": "1ab1c3f2", "metadata": {}, "outputs": [], "source": ["# La fonction sigmoide\n", "def sigmoid(x):\n", "    return 1 / (1 + np.exp(-x))"]}, {"cell_type": "code", "execution_count": 6, "id": "e91bd550", "metadata": {}, "outputs": [], "source": ["def initialisation(X):\n", "    W = np.random.randn(X.shape[1], 1)\n", "    b = np.random.randn(1)\n", "    return (W, b)"]}, {"cell_type": "code", "execution_count": 7, "id": "c0eea391", "metadata": {}, "outputs": [], "source": ["# <PERSON><PERSON><PERSON><PERSON>\n", "def modele(X, W, b):\n", "    Z = X.dot(W) + b\n", "    A = sigmoid(Z)\n", "    return A"]}, {"cell_type": "markdown", "id": "92dc2207", "metadata": {}, "source": ["W,b=initialisation(X)\n", "modele(X, W, b)"]}, {"cell_type": "markdown", "id": "49599deb", "metadata": {}, "source": ["### 3. <PERSON><PERSON><PERSON> "]}, {"cell_type": "code", "execution_count": 8, "id": "b01a2e59", "metadata": {}, "outputs": [], "source": ["def log_loss(y, A):\n", "    return 1/len(y) * np.sum(-y * np.log(A) - (1 - y) * np.log(1 - A))"]}, {"cell_type": "markdown", "id": "4d2bcd83", "metadata": {}, "source": ["### 4. Optimisation - Gradient et Descente de Gradient"]}, {"cell_type": "code", "execution_count": 9, "id": "78ddbff0", "metadata": {}, "outputs": [], "source": ["def gradients(X, A, y):\n", "    dW = 1/len(y) * np.dot(X.T, A - y)\n", "    db = 1/len(y) * np.sum(A - y)\n", "    return (dW, db)"]}, {"cell_type": "code", "execution_count": 10, "id": "8de052c9", "metadata": {}, "outputs": [], "source": ["def optimisation(X, W, b, A, y, learning_rate):\n", "    dW, db = gradients(X, A, y)\n", "    W = W - learning_rate * dW\n", "    b = b - learning_rate * db\n", "    return (W, b)"]}, {"cell_type": "markdown", "id": "a5c6e0e2", "metadata": {}, "source": ["### 5. <PERSON><PERSON><PERSON>"]}, {"cell_type": "code", "execution_count": 11, "id": "f4b7fbed", "metadata": {}, "outputs": [], "source": ["def predict(X, W, b):\n", "    A = modele(X, W, b)\n", "    print(A)\n", "    return A >= 0.5"]}, {"cell_type": "code", "execution_count": null, "id": "ab882c65", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "b8cefe90", "metadata": {}, "source": ["### 5. <PERSON><PERSON><PERSON><PERSON> final"]}, {"cell_type": "code", "execution_count": 12, "id": "6f61fb2d", "metadata": {}, "outputs": [], "source": ["def regression_logistique(X, y, learning_rate=0.1, n_iter=10000):\n", "  # Initialisation\n", "    W, b = initialisation(X)\n", "    loss_history = []\n", "  # Entrainement\n", "    for i in range(n_iter):\n", "        A = modele(X, W, b)\n", "        loss_history.append(log_loss(y, A))\n", "        W, b = optimisation(X, W, b, A, y, learning_rate=0.1)\n", "\n", "  # Prediction\n", "    plt.plot(loss_history)\n", "    plt.xlabel('n_iteration')\n", "    plt.ylabel('Log_loss')\n", "    plt.title('Evolution des erreurs')\n", "    return W,b"]}, {"cell_type": "code", "execution_count": 13, "id": "6f992b57", "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["W,b=regression_logistique(X, y)"]}, {"cell_type": "markdown", "id": "80c77dbf", "metadata": {}, "source": ["### 5. 1 Evaluation du modèle et Visualisation\n"]}, {"cell_type": "code", "execution_count": 14, "id": "cbbc3886", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[[6.88201815e-02]\n", " [2.19838952e-03]\n", " [9.07126176e-01]\n", " [7.19388266e-03]\n", " [5.77511570e-03]\n", " [5.77326536e-01]\n", " [7.57140226e-03]\n", " [2.12953777e-01]\n", " [6.71480653e-01]\n", " [3.47187113e-01]\n", " [9.05501088e-01]\n", " [1.56475663e-02]\n", " [1.12261555e-01]\n", " [9.50831040e-01]\n", " [9.25222048e-01]\n", " [6.07594999e-01]\n", " [9.88793457e-01]\n", " [1.71490849e-03]\n", " [9.28462971e-01]\n", " [9.98557199e-01]\n", " [9.18206276e-01]\n", " [7.25543602e-01]\n", " [9.99468905e-01]\n", " [3.08427595e-01]\n", " [8.63127717e-01]\n", " [1.34006918e-01]\n", " [9.91397305e-01]\n", " [8.28971785e-01]\n", " [9.66209959e-01]\n", " [9.55590835e-01]\n", " [6.82488919e-02]\n", " [9.58038978e-01]\n", " [7.13432182e-01]\n", " [4.30202760e-01]\n", " [3.67706808e-01]\n", " [9.90172048e-01]\n", " [9.64748469e-01]\n", " [4.13652901e-01]\n", " [9.08207491e-01]\n", " [1.53229132e-01]\n", " [1.16753259e-03]\n", " [9.57040106e-01]\n", " [1.47459643e-01]\n", " [3.25800086e-02]\n", " [9.47936242e-01]\n", " [4.83003401e-01]\n", " [2.63653460e-01]\n", " [4.75380427e-01]\n", " [9.94982064e-01]\n", " [1.31264643e-02]\n", " [8.79645493e-01]\n", " [9.98947065e-01]\n", " [9.76754284e-01]\n", " [2.68657397e-01]\n", " [1.01922382e-02]\n", " [7.03869890e-01]\n", " [6.44956026e-01]\n", " [9.30936221e-01]\n", " [9.94420075e-01]\n", " [9.79715039e-01]\n", " [9.30255812e-01]\n", " [4.71450905e-02]\n", " [1.64770227e-01]\n", " [2.25807427e-02]\n", " [9.98427593e-01]\n", " [5.01246358e-01]\n", " [6.99937064e-01]\n", " [1.45246007e-02]\n", " [2.24841650e-01]\n", " [9.87303706e-01]\n", " [5.21189306e-01]\n", " [7.95951438e-01]\n", " [7.16278496e-01]\n", " [1.67459437e-01]\n", " [8.78299606e-01]\n", " [6.99087910e-02]\n", " [9.99387568e-01]\n", " [5.20116764e-01]\n", " [6.49209980e-02]\n", " [9.97987122e-01]\n", " [9.94063200e-01]\n", " [2.98475258e-01]\n", " [9.98649747e-01]\n", " [2.95964312e-02]\n", " [9.44423399e-01]\n", " [4.33744871e-03]\n", " [1.38963997e-01]\n", " [6.23351274e-02]\n", " [4.51533979e-01]\n", " [1.28860947e-01]\n", " [1.96214060e-01]\n", " [1.29131532e-01]\n", " [4.46581640e-03]\n", " [1.83040432e-01]\n", " [9.99561985e-01]\n", " [9.99306927e-01]\n", " [1.00912659e-01]\n", " [1.18147584e-01]\n", " [9.59046337e-01]\n", " [9.08414466e-01]\n", " [8.07238085e-01]\n", " [5.34987791e-01]\n", " [1.10587383e-01]\n", " [1.69682688e-01]\n", " [9.98957125e-01]\n", " [1.12676679e-01]\n", " [8.06840220e-03]\n", " [1.44121737e-02]\n", " [9.98735104e-01]\n", " [2.81134788e-03]\n", " [9.72711037e-01]\n", " [9.87010955e-01]\n", " [1.42557578e-01]\n", " [9.74426087e-01]\n", " [8.04910286e-01]\n", " [9.77144416e-01]\n", " [9.41633898e-02]\n", " [1.84624370e-03]\n", " [9.99923107e-01]\n", " [5.16754446e-01]\n", " [9.88903455e-01]\n", " [1.71918900e-01]\n", " [4.02091249e-01]\n", " [5.96227236e-01]\n", " [9.97344846e-01]\n", " [7.36283183e-02]\n", " [3.82531138e-04]\n", " [9.38454039e-01]\n", " [9.96243113e-01]\n", " [7.46491282e-02]\n", " [5.84730522e-01]\n", " [9.99038664e-01]\n", " [9.98917973e-01]\n", " [9.62258094e-01]\n", " [9.72412889e-01]\n", " [1.05235725e-01]\n", " [6.21280304e-01]\n", " [9.98495826e-01]\n", " [9.90397091e-01]\n", " [7.37430390e-01]\n", " [3.59420138e-01]\n", " [1.20799811e-01]\n", " [8.82121299e-02]\n", " [1.10094556e-02]\n", " [9.91787886e-01]\n", " [4.55593968e-01]\n", " [6.46028347e-03]\n", " [5.67086954e-01]\n", " [5.11200063e-04]\n", " [9.95932896e-01]\n", " [9.73206947e-01]\n", " [9.99303805e-01]\n", " [4.96798517e-02]\n", " [1.32481488e-02]\n", " [8.55693215e-01]\n", " [1.28240860e-02]\n", " [1.44373936e-03]\n", " [8.77659099e-01]\n", " [8.30104030e-01]\n", " [2.06320624e-01]\n", " [7.05712581e-01]\n", " [9.54357334e-01]\n", " [1.45964377e-02]\n", " [3.50274157e-01]\n", " [2.48743345e-03]\n", " [8.40388646e-02]\n", " [5.25385524e-01]\n", " [9.97360524e-01]\n", " [9.97559633e-01]\n", " [6.03407605e-04]\n", " [1.21901453e-02]\n", " [9.04789302e-01]\n", " [8.23630714e-01]\n", " [1.00466045e-02]\n", " [2.99364819e-01]\n", " [2.93790984e-02]\n", " [6.10385985e-03]\n", " [4.03375870e-03]\n", " [9.98965477e-01]\n", " [3.43991620e-01]\n", " [2.33128799e-02]\n", " [8.35005405e-02]\n", " [1.22736067e-03]\n", " [7.97847421e-01]\n", " [2.18725735e-02]\n", " [2.16199625e-02]\n", " [3.64355983e-01]\n", " [1.76657721e-02]\n", " [9.95924262e-01]\n", " [7.68672426e-01]\n", " [9.97028004e-01]\n", " [7.15876480e-02]\n", " [2.87627946e-03]\n", " [8.08927976e-04]\n", " [1.17209088e-02]\n", " [7.06870683e-01]\n", " [4.52995562e-03]\n", " [5.49601684e-02]\n", " [8.96086329e-01]\n", " [6.26483973e-01]]\n", "Accuracy= 0.865\n"]}], "source": ["from sklearn.metrics import accuracy_score\n", "y_pred = predict(X, W, b)\n", "print(\"Accuracy=\",accuracy_score(y, y_pred))"]}, {"cell_type": "code", "execution_count": 15, "id": "fc2e330e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[0.60836289]\n", "[ True]\n", "[0.00193366]\n", "[False]\n", "[0.3099988]\n", "[False]\n"]}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 900x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Visualiser le dataset\n", "fig, ax = plt.subplots(figsize=(9, 6))\n", "ax.scatter(X[:,0], X[:, 1], c=y, cmap='winter')\n", "\n", "# Dessiner la frontière de décision\n", "x1 = np.linspace(0, 1, 200)\n", "x2 = ( - W[0] * x1 - b) / W[1]\n", "ax.plot(x1, x2, c='orange', lw=3)\n", "\n", "# Prédire la classe de nouveaux éléments\n", "\n", "new_1=np.array([0.1,0.1])\n", "new_2=np.array([0.8,0.1])\n", "new_3=np.array([0.9,0.8])\n", "ax.scatter(new_1[0], new_1[1], c='r')\n", "ax.scatter(new_2[0], new_2[1], c='r')\n", "ax.scatter(new_3[0], new_3[1], c='r')\n", "print(predict(new_1, W, b))\n", "print(predict(new_2, W, b))\n", "print(predict(new_3, W, b))\n", "\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 16, "id": "d6db1aac", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[[6.88201815e-02]\n", " [2.19838952e-03]\n", " [9.07126176e-01]\n", " [7.19388266e-03]\n", " [5.77511570e-03]\n", " [5.77326536e-01]\n", " [7.57140226e-03]\n", " [2.12953777e-01]\n", " [6.71480653e-01]\n", " [3.47187113e-01]\n", " [9.05501088e-01]\n", " [1.56475663e-02]\n", " [1.12261555e-01]\n", " [9.50831040e-01]\n", " [9.25222048e-01]\n", " [6.07594999e-01]\n", " [9.88793457e-01]\n", " [1.71490849e-03]\n", " [9.28462971e-01]\n", " [9.98557199e-01]\n", " [9.18206276e-01]\n", " [7.25543602e-01]\n", " [9.99468905e-01]\n", " [3.08427595e-01]\n", " [8.63127717e-01]\n", " [1.34006918e-01]\n", " [9.91397305e-01]\n", " [8.28971785e-01]\n", " [9.66209959e-01]\n", " [9.55590835e-01]\n", " [6.82488919e-02]\n", " [9.58038978e-01]\n", " [7.13432182e-01]\n", " [4.30202760e-01]\n", " [3.67706808e-01]\n", " [9.90172048e-01]\n", " [9.64748469e-01]\n", " [4.13652901e-01]\n", " [9.08207491e-01]\n", " [1.53229132e-01]\n", " [1.16753259e-03]\n", " [9.57040106e-01]\n", " [1.47459643e-01]\n", " [3.25800086e-02]\n", " [9.47936242e-01]\n", " [4.83003401e-01]\n", " [2.63653460e-01]\n", " [4.75380427e-01]\n", " [9.94982064e-01]\n", " [1.31264643e-02]\n", " [8.79645493e-01]\n", " [9.98947065e-01]\n", " [9.76754284e-01]\n", " [2.68657397e-01]\n", " [1.01922382e-02]\n", " [7.03869890e-01]\n", " [6.44956026e-01]\n", " [9.30936221e-01]\n", " [9.94420075e-01]\n", " [9.79715039e-01]\n", " [9.30255812e-01]\n", " [4.71450905e-02]\n", " [1.64770227e-01]\n", " [2.25807427e-02]\n", " [9.98427593e-01]\n", " [5.01246358e-01]\n", " [6.99937064e-01]\n", " [1.45246007e-02]\n", " [2.24841650e-01]\n", " [9.87303706e-01]\n", " [5.21189306e-01]\n", " [7.95951438e-01]\n", " [7.16278496e-01]\n", " [1.67459437e-01]\n", " [8.78299606e-01]\n", " [6.99087910e-02]\n", " [9.99387568e-01]\n", " [5.20116764e-01]\n", " [6.49209980e-02]\n", " [9.97987122e-01]\n", " [9.94063200e-01]\n", " [2.98475258e-01]\n", " [9.98649747e-01]\n", " [2.95964312e-02]\n", " [9.44423399e-01]\n", " [4.33744871e-03]\n", " [1.38963997e-01]\n", " [6.23351274e-02]\n", " [4.51533979e-01]\n", " [1.28860947e-01]\n", " [1.96214060e-01]\n", " [1.29131532e-01]\n", " [4.46581640e-03]\n", " [1.83040432e-01]\n", " [9.99561985e-01]\n", " [9.99306927e-01]\n", " [1.00912659e-01]\n", " [1.18147584e-01]\n", " [9.59046337e-01]\n", " [9.08414466e-01]\n", " [8.07238085e-01]\n", " [5.34987791e-01]\n", " [1.10587383e-01]\n", " [1.69682688e-01]\n", " [9.98957125e-01]\n", " [1.12676679e-01]\n", " [8.06840220e-03]\n", " [1.44121737e-02]\n", " [9.98735104e-01]\n", " [2.81134788e-03]\n", " [9.72711037e-01]\n", " [9.87010955e-01]\n", " [1.42557578e-01]\n", " [9.74426087e-01]\n", " [8.04910286e-01]\n", " [9.77144416e-01]\n", " [9.41633898e-02]\n", " [1.84624370e-03]\n", " [9.99923107e-01]\n", " [5.16754446e-01]\n", " [9.88903455e-01]\n", " [1.71918900e-01]\n", " [4.02091249e-01]\n", " [5.96227236e-01]\n", " [9.97344846e-01]\n", " [7.36283183e-02]\n", " [3.82531138e-04]\n", " [9.38454039e-01]\n", " [9.96243113e-01]\n", " [7.46491282e-02]\n", " [5.84730522e-01]\n", " [9.99038664e-01]\n", " [9.98917973e-01]\n", " [9.62258094e-01]\n", " [9.72412889e-01]\n", " [1.05235725e-01]\n", " [6.21280304e-01]\n", " [9.98495826e-01]\n", " [9.90397091e-01]\n", " [7.37430390e-01]\n", " [3.59420138e-01]\n", " [1.20799811e-01]\n", " [8.82121299e-02]\n", " [1.10094556e-02]\n", " [9.91787886e-01]\n", " [4.55593968e-01]\n", " [6.46028347e-03]\n", " [5.67086954e-01]\n", " [5.11200063e-04]\n", " [9.95932896e-01]\n", " [9.73206947e-01]\n", " [9.99303805e-01]\n", " [4.96798517e-02]\n", " [1.32481488e-02]\n", " [8.55693215e-01]\n", " [1.28240860e-02]\n", " [1.44373936e-03]\n", " [8.77659099e-01]\n", " [8.30104030e-01]\n", " [2.06320624e-01]\n", " [7.05712581e-01]\n", " [9.54357334e-01]\n", " [1.45964377e-02]\n", " [3.50274157e-01]\n", " [2.48743345e-03]\n", " [8.40388646e-02]\n", " [5.25385524e-01]\n", " [9.97360524e-01]\n", " [9.97559633e-01]\n", " [6.03407605e-04]\n", " [1.21901453e-02]\n", " [9.04789302e-01]\n", " [8.23630714e-01]\n", " [1.00466045e-02]\n", " [2.99364819e-01]\n", " [2.93790984e-02]\n", " [6.10385985e-03]\n", " [4.03375870e-03]\n", " [9.98965477e-01]\n", " [3.43991620e-01]\n", " [2.33128799e-02]\n", " [8.35005405e-02]\n", " [1.22736067e-03]\n", " [7.97847421e-01]\n", " [2.18725735e-02]\n", " [2.16199625e-02]\n", " [3.64355983e-01]\n", " [1.76657721e-02]\n", " [9.95924262e-01]\n", " [7.68672426e-01]\n", " [9.97028004e-01]\n", " [7.15876480e-02]\n", " [2.87627946e-03]\n", " [8.08927976e-04]\n", " [1.17209088e-02]\n", " [7.06870683e-01]\n", " [4.52995562e-03]\n", " [5.49601684e-02]\n", " [8.96086329e-01]\n", " [6.26483973e-01]]\n", "[[86 14]\n", " [13 87]]\n"]}], "source": ["#Affichage de la matrice de confusion\n", "\n", "from sklearn.metrics import confusion_matrix\n", "y_pred = predict(X, W, b)\n", "cm = confusion_matrix(y, y_pred)\n", "print(cm)"]}, {"cell_type": "code", "execution_count": 17, "id": "1abc5628", "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 640x480 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Plot confusion matrix\n", "import seaborn as sns\n", "import pandas as pd\n", "# confusion matrix sns heatmap \n", "## https://www.kaggle.com/agungor2/various-confusion-matrix-plots\n", "ax = plt.axes()\n", "df_cm = cm\n", "sns.heatmap(df_cm, annot=True, annot_kws={\"size\": 30}, fmt='d',cmap=\"Blues\", ax = ax )\n", "ax.set_title('Confusion Matrix')\n", "plt.show()"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.13"}}, "nbformat": 4, "nbformat_minor": 5}