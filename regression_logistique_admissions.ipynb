{"cells": [{"cell_type": "markdown", "id": "a1b2c3d4", "metadata": {}, "source": ["# Régression Logistique Binaire - Prédiction d'Admissions\n", "\n", "Implémentation from scratch d'un modèle de régression logistique pour classer les admis et non admis selon les indicateurs GPA et GRE."]}, {"cell_type": "markdown", "id": "e1f2g3h4", "metadata": {}, "source": ["### 1. Importation des bibliothèques et chargement des données"]}, {"cell_type": "code", "execution_count": null, "id": "i5j6k7l8", "metadata": {}, "outputs": [], "source": ["#souhail ouargui\n", "import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.preprocessing import StandardScaler\n", "from sklearn.metrics import accuracy_score, confusion_matrix\n", "import seaborn as sns"]}, {"cell_type": "code", "execution_count": null, "id": "m9n0o1p2", "metadata": {}, "outputs": [], "source": ["#souhail ouargui\n", "# Chargement du dataset\n", "dataset = pd.read_csv('admissions - admissions.csv')\n", "print(\"Shape du dataset:\", dataset.shape)\n", "print(\"\\nPremières lignes:\")\n", "print(dataset.head())\n", "print(\"\\nInformations sur le dataset:\")\n", "print(dataset.info())\n", "print(\"\\nStatistiques descriptives:\")\n", "print(dataset.describe())"]}, {"cell_type": "code", "execution_count": null, "id": "q3r4s5t6", "metadata": {}, "outputs": [], "source": ["#souhail ouargui\n", "# Vérification de la distribution des classes\n", "print(\"Distribution des admissions:\")\n", "print(dataset['admit'].value_counts())\n", "print(\"\\nPourcentage d'admissions:\")\n", "print(dataset['admit'].value_counts(normalize=True) * 100)"]}, {"cell_type": "code", "execution_count": null, "id": "u7v8w9x0", "metadata": {}, "outputs": [], "source": ["#souhail ouargui\n", "# Préparation des données\n", "X = dataset[['gpa', 'gre']].values\n", "y = dataset['admit'].values.reshape(-1, 1)\n", "\n", "print(\"Shape de X:\", X.shape)\n", "print(\"Shape de y:\", y.shape)"]}, {"cell_type": "code", "execution_count": null, "id": "y1z2a3b4", "metadata": {}, "outputs": [], "source": ["#souhail ouargui\n", "# Normalisation des données\n", "scaler = StandardScaler()\n", "X_scaled = scaler.fit_transform(X)\n", "\n", "print(\"Données avant normalisation:\")\n", "print(\"GPA - Min:\", X[:, 0].min(), \"Max:\", X[:, 0].max())\n", "print(\"GRE - Min:\", X[:, 1].min(), \"Max:\", X[:, 1].max())\n", "print(\"\\nDonnées après normalisation:\")\n", "print(\"GPA - Min:\", X_scaled[:, 0].min(), \"Max:\", X_scaled[:, 0].max())\n", "print(\"GRE - Min:\", X_scaled[:, 1].min(), \"Max:\", X_scaled[:, 1].max())"]}, {"cell_type": "code", "execution_count": null, "id": "c5d6e7f8", "metadata": {}, "outputs": [], "source": ["#souhail ouargui\n", "# Visualisation des données\n", "plt.figure(figsize=(12, 5))\n", "\n", "plt.subplot(1, 2, 1)\n", "plt.scatter(X[:, 0], X[:, 1], c=y.ravel(), cmap='winter', alpha=0.7)\n", "plt.xlabel('GPA')\n", "plt.ylabel('GRE')\n", "plt.title('Données originales')\n", "plt.colorbar(label='Admission (0=Non, 1=Oui)')\n", "\n", "plt.subplot(1, 2, 2)\n", "plt.scatter(X_scaled[:, 0], X_scaled[:, 1], c=y.ravel(), cmap='winter', alpha=0.7)\n", "plt.xlabel('GPA (normalisé)')\n", "plt.ylabel('GRE (normalisé)')\n", "plt.title('Données normalisées')\n", "plt.colorbar(label='Admission (0=Non, 1=Oui)')\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "g9h0i1j2", "metadata": {}, "outputs": [], "source": ["#souhail ouargui\n", "# Séparation en ensemble d'entraînement et de test\n", "X_train, X_test, y_train, y_test = train_test_split(X_scaled, y, test_size=0.2, random_state=42, stratify=y)\n", "\n", "print(\"Taille ensemble d'entraînement:\", X_train.shape[0])\n", "print(\"Taille ensemble de test:\", X_test.shape[0])\n", "print(\"\\nDistribution dans l'ensemble d'entraînement:\")\n", "print(\"Non admis:\", np.sum(y_train == 0), \"Admis:\", np.sum(y_train == 1))\n", "print(\"\\nDistribution dans l'ensemble de test:\")\n", "print(\"Non admis:\", np.sum(y_test == 0), \"Admis:\", np.sum(y_test == 1))"]}, {"cell_type": "markdown", "id": "k3l4m5n6", "metadata": {}, "source": ["### 2. <PERSON><PERSON><PERSON><PERSON>"]}, {"cell_type": "code", "execution_count": null, "id": "o7p8q9r0", "metadata": {}, "outputs": [], "source": ["#souhail ouargui\n", "# La fonction sigmoide\n", "def sigmoid(x):\n", "    # Éviter l'overflow en limitant les valeurs extrêmes\n", "    x = np.clip(x, -500, 500)\n", "    return 1 / (1 + np.exp(-x))"]}, {"cell_type": "code", "execution_count": null, "id": "s1t2u3v4", "metadata": {}, "outputs": [], "source": ["#souhail ouargui\n", "def initialisation(X):\n", "    W = np.random.randn(X.shape[1], 1) * 0.01\n", "    b = np.random.randn(1) * 0.01\n", "    return (W, b)"]}, {"cell_type": "code", "execution_count": null, "id": "w5x6y7z8", "metadata": {}, "outputs": [], "source": ["#souhail ouargui\n", "# <PERSON><PERSON><PERSON><PERSON>\n", "def modele(X, W, b):\n", "    Z = X.dot(W) + b\n", "    A = sigmoid(Z)\n", "    return A"]}, {"cell_type": "markdown", "id": "a9b0c1d2", "metadata": {}, "source": ["### 3. <PERSON><PERSON><PERSON>"]}, {"cell_type": "code", "execution_count": null, "id": "e3f4g5h6", "metadata": {}, "outputs": [], "source": ["#souhail ouargui\n", "def log_loss(y, A):\n", "    # Éviter log(0) en ajoutant une petite valeur epsilon\n", "    epsilon = 1e-15\n", "    A = np.clip(A, epsilon, 1 - epsilon)\n", "    return 1/len(y) * np.sum(-y * np.log(A) - (1 - y) * np.log(1 - A))"]}, {"cell_type": "markdown", "id": "i7j8k9l0", "metadata": {}, "source": ["### 4. Optimisation - Gradient et Descente de Gradient"]}, {"cell_type": "code", "execution_count": null, "id": "m1n2o3p4", "metadata": {}, "outputs": [], "source": ["#souhail ouargui\n", "def gradients(X, A, y):\n", "    dW = 1/len(y) * np.dot(X.T, A - y)\n", "    db = 1/len(y) * np.sum(A - y)\n", "    return (dW, db)"]}, {"cell_type": "code", "execution_count": null, "id": "q5r6s7t8", "metadata": {}, "outputs": [], "source": ["#souhail ouargui\n", "def optimisation(X, W, b, A, y, learning_rate):\n", "    dW, db = gradients(X, A, y)\n", "    W = W - learning_rate * dW\n", "    b = b - learning_rate * db\n", "    return (W, b)"]}, {"cell_type": "markdown", "id": "u9v0w1x2", "metadata": {}, "source": ["### 5. <PERSON><PERSON><PERSON>"]}, {"cell_type": "code", "execution_count": null, "id": "y3z4a5b6", "metadata": {}, "outputs": [], "source": ["#souhail ouargui\n", "def predict(X, W, b):\n", "    A = modele(X, W, b)\n", "    return A >= 0.5"]}, {"cell_type": "markdown", "id": "c7d8e9f0", "metadata": {}, "source": ["### 6. <PERSON><PERSON><PERSON><PERSON> final"]}, {"cell_type": "code", "execution_count": null, "id": "g1h2i3j4", "metadata": {}, "outputs": [], "source": ["#souhail ouargui\n", "def regression_logistique(X, y, learning_rate=0.01, n_iter=1000):\n", "    # Initialisation\n", "    W, b = initialisation(X)\n", "    loss_history = []\n", "    \n", "    # Entrainement\n", "    for i in range(n_iter):\n", "        A = modele(X, W, b)\n", "        loss = log_loss(y, A)\n", "        loss_history.append(loss)\n", "        W, b = optimisation(X, W, b, A, y, learning_rate)\n", "        \n", "        # Affichage du coût toutes les 100 itérations\n", "        if i % 100 == 0:\n", "            print(f\"Coût après {i} itérations: {loss:.6f}\")\n", "    \n", "    # Graphique de l'évolution du coût\n", "    plt.figure(figsize=(10, 6))\n", "    plt.plot(loss_history)\n", "    plt.xlabel('Nombre d\\'itérations')\n", "    plt.ylabel('Log Loss')\n", "    plt.title('Évolution de la fonction de coût')\n", "    plt.grid(True)\n", "    plt.show()\n", "    \n", "    return W, b"]}, {"cell_type": "markdown", "id": "k5l6m7n8", "metadata": {}, "source": ["### 7. Entraînement du modèle"]}, {"cell_type": "code", "execution_count": null, "id": "o9p0q1r2", "metadata": {}, "outputs": [], "source": ["#souhail ouargui\n", "# Entraînement du modèle\n", "print(\"Entraînement du modèle de régression logistique...\")\n", "W, b = regression_logistique(X_train, y_train, learning_rate=0.1, n_iter=2000)\n", "\n", "print(f\"\\nParamètres finaux:\")\n", "print(f\"W (poids): {W.ravel()}\")\n", "print(f\"b (biais): {b[0]:.6f}\")"]}, {"cell_type": "markdown", "id": "s3t4u5v6", "metadata": {}, "source": ["### 8. Évaluation du modèle"]}, {"cell_type": "code", "execution_count": null, "id": "w7x8y9z0", "metadata": {}, "outputs": [], "source": ["#souhail ouargui\n", "# Prédictions sur l'ensemble d'entraînement\n", "y_pred_train = predict(X_train, W, b)\n", "accuracy_train = accuracy_score(y_train, y_pred_train)\n", "print(f\"Accuracy sur l'ensemble d'entraînement: {accuracy_train:.3f}\")\n", "\n", "# Prédictions sur l'ensemble de test\n", "y_pred_test = predict(X_test, W, b)\n", "accuracy_test = accuracy_score(y_test, y_pred_test)\n", "print(f\"Accuracy sur l'ensemble de test: {accuracy_test:.3f}\")"]}, {"cell_type": "code", "execution_count": null, "id": "a1b2c3d4", "metadata": {}, "outputs": [], "source": ["#souhail ouargui\n", "# <PERSON><PERSON> de confusion\n", "cm = confusion_matrix(y_test, y_pred_test)\n", "print(\"\\nMatrice de confusion:\")\n", "print(cm)\n", "\n", "# Visualisation de la matrice de confusion\n", "plt.figure(figsize=(8, 6))\n", "sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', \n", "            xticklabels=['Non admis', 'Admis'], \n", "            yticklabels=['Non admis', 'Admis'])\n", "plt.xlabel('Prédictions')\n", "plt.ylabel('Vraies valeurs')\n", "plt.title('<PERSON><PERSON> de confusion')\n", "plt.show()"]}, {"cell_type": "markdown", "id": "e5f6g7h8", "metadata": {}, "source": ["### 9. Visualisation de la frontière de décision"]}, {"cell_type": "code", "execution_count": null, "id": "i9j0k1l2", "metadata": {}, "outputs": [], "source": ["#souhail ouargui\n", "# Fonction pour tracer la frontière de décision\n", "def plot_decision_boundary(X, y, W, b, title=\"Frontière de décision\"):\n", "    plt.figure(figsize=(10, 8))\n", "    \n", "    # <PERSON><PERSON>er une grille de points\n", "    h = 0.02\n", "    x_min, x_max = X[:, 0].min() - 1, X[:, 0].max() + 1\n", "    y_min, y_max = X[:, 1].min() - 1, X[:, 1].max() + 1\n", "    xx, yy = np.meshgrid(np.arange(x_min, x_max, h),\n", "                         np.arange(y_min, y_max, h))\n", "    \n", "    # Prédire sur la grille\n", "    grid_points = np.c_[xx.ravel(), yy.ravel()]\n", "    Z = modele(grid_points, W, b)\n", "    Z = Z.reshape(xx.shape)\n", "    \n", "    # Tracer la frontière de décision\n", "    plt.contour(xx, yy, Z, levels=[0.5], colors='orange', linewidths=3)\n", "    plt.contourf(xx, yy, Z, levels=50, alpha=0.3, cmap='RdYlBu')\n", "    \n", "    # Tracer les points de données\n", "    scatter = plt.scatter(X[:, 0], X[:, 1], c=y.ravel(), cmap='winter', \n", "                         edgecolors='black', alpha=0.7)\n", "    plt.colorbar(scatter, label='Admission (0=Non, 1=Oui)')\n", "    \n", "    plt.xlabel('GPA (normalisé)')\n", "    plt.ylabel('GRE (normalisé)')\n", "    plt.title(title)\n", "    plt.grid(True, alpha=0.3)\n", "    plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "m3n4o5p6", "metadata": {}, "outputs": [], "source": ["#souhail ouargui\n", "# Visualisation sur l'ensemble d'entraînement\n", "plot_decision_boundary(X_train, y_train, W, b, \n", "                      \"Frontière de décision - Ensemble d'entraînement\")"]}, {"cell_type": "code", "execution_count": null, "id": "q7r8s9t0", "metadata": {}, "outputs": [], "source": ["#souhail ouargui\n", "# Visualisation sur l'ensemble de test\n", "plot_decision_boundary(X_test, y_test, W, b, \n", "                      \"Frontière de décision - Ensemble de test\")"]}, {"cell_type": "markdown", "id": "u1v2w3x4", "metadata": {}, "source": ["### 10. Prédiction sur de nouveaux exemples"]}, {"cell_type": "code", "execution_count": null, "id": "y5z6a7b8", "metadata": {}, "outputs": [], "source": ["#souhail ouargui\n", "# Exemples de nouveaux étudiants (GPA, GRE)\n", "nouveaux_etudiants = np.array([\n", "    [3.5, 650],  # <PERSON>\n", "    [2.8, 500],  # <PERSON><PERSON><PERSON><PERSON> moyen\n", "    [3.9, 750],  # Excellent étudiant\n", "    [2.5, 450]   # <PERSON><PERSON>dian<PERSON> faible\n", "])\n", "\n", "# Normaliser les nouveaux exemples avec le même scaler\n", "nouveaux_etudiants_scaled = scaler.transform(nouveaux_etudiants)\n", "\n", "# Prédictions\n", "probabilites = modele(nouveaux_etudiants_scaled, W, b)\n", "predictions = predict(nouveaux_etudiants_scaled, W, b)\n", "\n", "print(\"Prédictions pour de nouveaux étudiants:\")\n", "print(\"GPA\\tGRE\\tProbabilité\\tPrédiction\")\n", "print(\"-\" * 45)\n", "for i, (etudiant, prob, pred) in enumerate(zip(nouveaux_etudiants, probabilites, predictions)):\n", "    gpa, gre = etudiant\n", "    admission = \"Admis\" if pred[0] else \"Non admis\"\n", "    print(f\"{gpa:.1f}\\t{gre:.0f}\\t{prob[0]:.3f}\\t\\t{admission}\")"]}, {"cell_type": "code", "execution_count": null, "id": "c9d0e1f2", "metadata": {}, "outputs": [], "source": ["#souhail ouargui\n", "# Visualisation des nouveaux exemples sur la frontière de décision\n", "plt.figure(figsize=(10, 8))\n", "\n", "# Tracer la frontière de décision avec les données de test\n", "h = 0.02\n", "x_min, x_max = X_test[:, 0].min() - 1, X_test[:, 0].max() + 1\n", "y_min, y_max = X_test[:, 1].min() - 1, X_test[:, 1].max() + 1\n", "xx, yy = np.meshgrid(np.arange(x_min, x_max, h),\n", "                     np.arange(y_min, y_max, h))\n", "\n", "grid_points = np.c_[xx.ravel(), yy.ravel()]\n", "Z = modele(grid_points, W, b)\n", "Z = Z.reshape(xx.shape)\n", "\n", "plt.contour(xx, yy, Z, levels=[0.5], colors='orange', linewidths=3, \n", "           linestyles='--', label='Frontière de décision')\n", "plt.contourf(xx, yy, Z, levels=50, alpha=0.3, cmap='RdYlBu')\n", "\n", "# Tracer les données de test\n", "scatter1 = plt.scatter(X_test[:, 0], X_test[:, 1], c=y_test.ravel(), \n", "                      cmap='winter', alpha=0.6, s=50, label='Données de test')\n", "\n", "# Tracer les nouveaux exemples\n", "scatter2 = plt.scatter(nouveaux_etudiants_scaled[:, 0], nouveaux_etudiants_scaled[:, 1], \n", "                      c='red', s=200, marker='*', edgecolors='black', \n", "                      label='Nouveaux étudiants')\n", "\n", "plt.xlabel('GPA (normalisé)')\n", "plt.ylabel('GRE (normalisé)')\n", "plt.title('Prédictions sur de nouveaux exemples')\n", "plt.legend()\n", "plt.grid(True, alpha=0.3)\n", "plt.show()"]}, {"cell_type": "markdown", "id": "g3h4i5j6", "metadata": {}, "source": ["### 11. Résumé et Analyse des Résultats"]}, {"cell_type": "code", "execution_count": null, "id": "k7l8m9n0", "metadata": {}, "outputs": [], "source": ["#souhail ouargui\n", "# Analyse des coefficients du modèle\n", "print(\"=\" * 50)\n", "print(\"RÉSUMÉ DU MODÈLE DE RÉGRESSION LOGISTIQUE\")\n", "print(\"=\" * 50)\n", "print(f\"\\nCoefficients du modèle:\")\n", "print(f\"  - Poids pour GPA (W[0]): {W[0][0]:.4f}\")\n", "print(f\"  - Poids pour GRE (W[1]): {W[1][0]:.4f}\")\n", "print(f\"  - B<PERSON><PERSON> (b): {b[0]:.4f}\")\n", "\n", "print(f\"\\nInterprétation:\")\n", "if W[0][0] > 0:\n", "    print(f\"  - Un GPA plus élevé augmente la probabilité d'admission\")\n", "else:\n", "    print(f\"  - Un GPA plus élevé diminue la probabilité d'admission\")\n", "    \n", "if W[1][0] > 0:\n", "    print(f\"  - Un score GRE plus élevé augmente la probabilité d'admission\")\n", "else:\n", "    print(f\"  - Un score GRE plus élevé diminue la probabilité d'admission\")\n", "\n", "print(f\"\\nPerformances du modèle:\")\n", "print(f\"  - Accuracy sur l'entraînement: {accuracy_train:.1%}\")\n", "print(f\"  - Accuracy sur le test: {accuracy_test:.1%}\")\n", "\n", "# Calcul des métriques supplémentaires\n", "from sklearn.metrics import precision_score, recall_score, f1_score\n", "\n", "precision = precision_score(y_test, y_pred_test)\n", "recall = recall_score(y_test, y_pred_test)\n", "f1 = f1_score(y_test, y_pred_test)\n", "\n", "print(f\"  - Précision: {precision:.1%}\")\n", "print(f\"  - Rappel: {recall:.1%}\")\n", "print(f\"  - F1-Score: {f1:.1%}\")\n", "\n", "print(f\"\\nÉquation de la frontière de décision:\")\n", "print(f\"  {W[0][0]:.4f} * GPA + {W[1][0]:.4f} * GRE + {b[0]:.4f} = 0\")"]}, {"cell_type": "markdown", "id": "o1p2q3r4", "metadata": {}, "source": ["### 12. Comparaison avec la régression logistique de scikit-learn"]}, {"cell_type": "code", "execution_count": null, "id": "s5t6u7v8", "metadata": {}, "outputs": [], "source": ["#souhail ouargui\n", "# Comparaison avec scikit-learn\n", "from sklearn.linear_model import LogisticRegression\n", "\n", "# Entraînement avec scikit-learn\n", "sklearn_model = LogisticRegression(random_state=42)\n", "sklearn_model.fit(X_train, y_train.ravel())\n", "\n", "# Prédictions\n", "y_pred_sklearn = sklearn_model.predict(X_test)\n", "accuracy_sklearn = accuracy_score(y_test, y_pred_sklearn)\n", "\n", "print(\"Comparaison des modèles:\")\n", "print(\"-\" * 40)\n", "print(f\"Notre implémentation:\")\n", "print(f\"  - Accuracy: {accuracy_test:.1%}\")\n", "print(f\"  - Coefficients: {W.ravel()}\")\n", "print(f\"  - Biais: {b[0]:.4f}\")\n", "\n", "print(f\"\\nScikit-learn:\")\n", "print(f\"  - Accuracy: {accuracy_sklearn:.1%}\")\n", "print(f\"  - Coefficients: {sklearn_model.coef_[0]}\")\n", "print(f\"  - Biais: {sklearn_model.intercept_[0]:.4f}\")\n", "\n", "print(f\"\\nDifférence d'accuracy: {abs(accuracy_test - accuracy_sklearn):.1%}\")"]}, {"cell_type": "markdown", "id": "w9x0y1z2", "metadata": {}, "source": ["### 13. Conc<PERSON>\n", "\n", "**Résumé de l'implémentation:**\n", "\n", "1. **Mod<PERSON><PERSON> implément<PERSON>:** Régression logistique binaire from scratch\n", "2. **Variables d'entrée:** GPA et GRE (normalisées)\n", "3. **Variable cible:** Admission (0 = Non admis, 1 = Admis)\n", "4. **Algorithme d'optimisation:** Descente de gradient\n", "5. **Fonction de coût:** Log-loss (entropie croisée binaire)\n", "\n", "**Points clés de l'implémentation:**\n", "- Normalisation des données pour améliorer la convergence\n", "- Gestion des cas limites (overflow, log(0))\n", "- Visualisation de la frontière de décision linéaire\n", "- Évaluation complète avec métriques de performance\n", "- Comparaison avec l'implémentation de référence (scikit-learn)\n", "\n", "**Applications possibles:**\n", "- Aide à la décision pour les admissions universitaires\n", "- Identification des facteurs les plus importants\n", "- Prédiction de probabilités d'admission pour de nouveaux candidats"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.0"}}, "nbformat": 4, "nbformat_minor": 5}