{"cells": [{"cell_type": "markdown", "id": "a1b2c3d4", "metadata": {}, "source": ["# Régression Logistique Binaire Non Linéaire - Prédiction d'Admissions\n", "\n", "Implémentation from scratch d'un modèle de régression logistique avec frontière de décision non linéaire pour classer les admis et non admis selon les indicateurs GPA et GRE."]}, {"cell_type": "markdown", "id": "e1f2g3h4", "metadata": {}, "source": ["### 1. Importation des bibliothèques et chargement des données"]}, {"cell_type": "code", "execution_count": null, "id": "i5j6k7l8", "metadata": {}, "outputs": [], "source": ["#souhail ouargui\n", "import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.preprocessing import StandardScaler, PolynomialFeatures\n", "from sklearn.metrics import accuracy_score, confusion_matrix, precision_score, recall_score, f1_score\n", "import seaborn as sns\n", "from itertools import combinations_with_replacement"]}, {"cell_type": "code", "execution_count": null, "id": "m9n0o1p2", "metadata": {}, "outputs": [], "source": ["#souhail ouargui\n", "# Chargement du dataset\n", "dataset = pd.read_csv('admissions - admissions.csv')\n", "print(\"Shape du dataset:\", dataset.shape)\n", "print(\"\\nPremières lignes:\")\n", "print(dataset.head())\n", "print(\"\\nDistribution des admissions:\")\n", "print(dataset['admit'].value_counts())\n", "print(\"\\nPourcentage d'admissions:\")\n", "print(dataset['admit'].value_counts(normalize=True) * 100)"]}, {"cell_type": "code", "execution_count": null, "id": "q3r4s5t6", "metadata": {}, "outputs": [], "source": ["#souhail ouargui\n", "# Préparation des données\n", "X = dataset[['gpa', 'gre']].values\n", "y = dataset['admit'].values\n", "\n", "print(\"Shape de X:\", X.shape)\n", "print(\"Shape de y:\", y.shape)\n", "print(\"\\nDonnées originales:\")\n", "print(\"GPA - Min:\", X[:, 0].min(), \"Max:\", X[:, 0].max())\n", "print(\"GRE - Min:\", X[:, 1].min(), \"Max:\", X[:, 1].max())"]}, {"cell_type": "code", "execution_count": null, "id": "u7v8w9x0", "metadata": {}, "outputs": [], "source": ["#souhail ouargui\n", "# Séparation train/test AVANT le scaling (bonne pratique)\n", "X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.33, random_state=42)\n", "\n", "print(\"Taille ensemble d'entraînement:\", X_train.shape[0])\n", "print(\"Taille ensemble de test:\", X_test.shape[0])\n", "print(\"\\nDistribution dans l'ensemble d'entraînement:\")\n", "print(\"Non admis:\", np.sum(y_train == 0), \"Admis:\", np.sum(y_train == 1))\n", "print(\"\\nDistribution dans l'ensemble de test:\")\n", "print(\"Non admis:\", np.sum(y_test == 0), \"Admis:\", np.sum(y_test == 1))"]}, {"cell_type": "code", "execution_count": null, "id": "y1z2a3b4", "metadata": {}, "outputs": [], "source": ["#souhail ouargui\n", "# Normalisation basée uniquement sur X_train (évite le data leakage)\n", "scaler = StandardScaler()\n", "X_train_scaled = scaler.fit_transform(X_train)\n", "X_test_scaled = scaler.transform(X_test)  # Important : on transforme SEULEMENT avec les stats de X_train\n", "\n", "print(\"Données d'entraînement après normalisation:\")\n", "print(\"GPA - Min:\", X_train_scaled[:, 0].min(), \"Max:\", X_train_scaled[:, 0].max())\n", "print(\"GRE - Min:\", X_train_scaled[:, 1].min(), \"Max:\", X_train_scaled[:, 1].max())\n", "\n", "# S'assurer que y est en colonne\n", "y_train = y_train.reshape(-1, 1)\n", "y_test = y_test.reshape(-1, 1)"]}, {"cell_type": "markdown", "id": "c5d6e7f8", "metadata": {}, "source": ["### 2. Création des features polynomiales pour la non-linéarité"]}, {"cell_type": "code", "execution_count": null, "id": "g9h0i1j2", "metadata": {}, "outputs": [], "source": ["#souhail ouargui\n", "def create_polynomial_features(X, degree=2):\n", "    \"\"\"\n", "    Crée des features polynomiales from scratch\n", "    Pour degree=2 avec 2 features [x1, x2], on obtient:\n", "    [1, x1, x2, x1^2, x1*x2, x2^2]\n", "    \"\"\"\n", "    n_samples, n_features = X.shape\n", "    \n", "    # Commencer avec le terme constant (biais)\n", "    poly_features = [np.ones((n_samples, 1))]\n", "    \n", "    # Ajouter les features originales\n", "    poly_features.append(X)\n", "    \n", "    # Ajouter les termes polynomiaux\n", "    if degree >= 2:\n", "        # Termes quadratiques purs (x1^2, x2^2)\n", "        for i in range(n_features):\n", "            poly_features.append((X[:, i] ** 2).reshape(-1, 1))\n", "        \n", "        # Termes croisés (x1*x2)\n", "        for i in range(n_features):\n", "            for j in range(i+1, n_features):\n", "                poly_features.append((X[:, i] * X[:, j]).reshape(-1, 1))\n", "    \n", "    if degree >= 3:\n", "        # Termes cubiques\n", "        for i in range(n_features):\n", "            poly_features.append((X[:, i] ** 3).reshape(-1, 1))\n", "        \n", "        # Termes croisés cubiques\n", "        for i in range(n_features):\n", "            for j in range(i+1, n_features):\n", "                poly_features.append((X[:, i]**2 * X[:, j]).reshape(-1, 1))\n", "                poly_features.append((X[:, i] * X[:, j]**2).reshape(-1, 1))\n", "    \n", "    return np.hstack(poly_features)\n", "\n", "# C<PERSON>er les features polynomiales\n", "degree = 2\n", "X_train_poly = create_polynomial_features(X_train_scaled, degree=degree)\n", "X_test_poly = create_polynomial_features(X_test_scaled, degree=degree)\n", "\n", "print(f\"Features originales: {X_train_scaled.shape[1]}\")\n", "print(f\"Features polynomiales (degré {degree}): {X_train_poly.shape[1]}\")\n", "print(f\"\\nNoms des features:\")\n", "feature_names = ['1', 'gpa', 'gre', 'gpa^2', 'gre^2', 'gpa*gre']\n", "for i, name in enumerate(feature_names):\n", "    print(f\"  {i}: {name}\")"]}, {"cell_type": "code", "execution_count": null, "id": "k3l4m5n6", "metadata": {}, "outputs": [], "source": ["#souhail ouargui\n", "# Visualisation des données originales\n", "plt.figure(figsize=(12, 5))\n", "\n", "plt.subplot(1, 2, 1)\n", "plt.scatter(X_train[:, 0], X_train[:, 1], c=y_train.ravel(), cmap='winter', alpha=0.7)\n", "plt.xlabel('GPA')\n", "plt.ylabel('GRE')\n", "plt.title('Données d\\'entraînement (originales)')\n", "plt.colorbar(label='Admission (0=Non, 1=Oui)')\n", "\n", "plt.subplot(1, 2, 2)\n", "plt.scatter(X_train_scaled[:, 0], X_train_scaled[:, 1], c=y_train.ravel(), cmap='winter', alpha=0.7)\n", "plt.xlabel('GPA (normalisé)')\n", "plt.ylabel('GRE (normalisé)')\n", "plt.title('Données d\\'entraînement (normalisées)')\n", "plt.colorbar(label='Admission (0=Non, 1=Oui)')\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "id": "o7p8q9r0", "metadata": {}, "source": ["### 3. <PERSON><PERSON><PERSON><PERSON> de régression logistique (même structure que le modèle linéaire)"]}, {"cell_type": "code", "execution_count": null, "id": "s1t2u3v4", "metadata": {}, "outputs": [], "source": ["#souhail ouargui\n", "# La fonction sigmoide\n", "def sigmoid(x):\n", "    # Éviter l'overflow en limitant les valeurs extrêmes\n", "    x = np.clip(x, -500, 500)\n", "    return 1 / (1 + np.exp(-x))"]}, {"cell_type": "code", "execution_count": null, "id": "w5x6y7z8", "metadata": {}, "outputs": [], "source": ["#souhail ouargui\n", "def initialisation(X):\n", "    # Pour les features polynomiales, on initialise avec des valeurs plus petites\n", "    W = np.random.randn(X.shape[1], 1) * 0.001\n", "    return W"]}, {"cell_type": "code", "execution_count": null, "id": "a9b0c1d2", "metadata": {}, "outputs": [], "source": ["#souhail ouargui\n", "# Modèle (pas de biais séparé car inclus dans les features polynomiales)\n", "def modele(X, W):\n", "    Z = X.dot(W)\n", "    A = sigmoid(Z)\n", "    return A"]}, {"cell_type": "code", "execution_count": null, "id": "e3f4g5h6", "metadata": {}, "outputs": [], "source": ["#souhail ouargui\n", "def log_loss(y, A):\n", "    # Éviter log(0) en ajoutant une petite valeur epsilon\n", "    epsilon = 1e-15\n", "    A = np.clip(A, epsilon, 1 - epsilon)\n", "    return 1/len(y) * np.sum(-y * np.log(A) - (1 - y) * np.log(1 - A))"]}, {"cell_type": "code", "execution_count": null, "id": "i7j8k9l0", "metadata": {}, "outputs": [], "source": ["#souhail ouargui\n", "def gradients(X, A, y):\n", "    dW = 1/len(y) * np.dot(X.T, A - y)\n", "    return dW"]}, {"cell_type": "code", "execution_count": null, "id": "m1n2o3p4", "metadata": {}, "outputs": [], "source": ["#souhail ouargui\n", "def optimisation(X, W, A, y, learning_rate):\n", "    dW = gradients(X, A, y)\n", "    W = W - learning_rate * dW\n", "    return W"]}, {"cell_type": "code", "execution_count": null, "id": "q5r6s7t8", "metadata": {}, "outputs": [], "source": ["#souhail ouargui\n", "def predict(X, W):\n", "    A = modele(X, W)\n", "    return A >= 0.5"]}, {"cell_type": "markdown", "id": "u9v0w1x2", "metadata": {}, "source": ["### 4. <PERSON><PERSON><PERSON><PERSON> final avec régularisation"]}, {"cell_type": "code", "execution_count": null, "id": "y3z4a5b6", "metadata": {}, "outputs": [], "source": ["#souhail ouargui\n", "def regression_logistique(X, y, learning_rate=0.01, n_iter=1000, lambda_reg=0.01):\n", "    # Initialisation\n", "    W = initialisation(X)\n", "    loss_history = []\n", "    \n", "    # Entrainement\n", "    for i in range(n_iter):\n", "        A = modele(X, W)\n", "        \n", "        # Calcul du coût avec régularisation L2 (exclut le biais)\n", "        loss = log_loss(y, A)\n", "        reg_loss = loss + lambda_reg * np.sum(W[1:] ** 2)  # Exclut W[0] (biais)\n", "        loss_history.append(reg_loss)\n", "        \n", "        # Gradient avec régularisation\n", "        dW = gradients(X, A, y)\n", "        dW[1:] += 2 * lambda_reg * W[1:]  # Régularisation seulement sur les poids, pas le biais\n", "        \n", "        # Mise à jour\n", "        W = W - learning_rate * dW\n", "        \n", "        # Affichage du coût toutes les 200 itérations\n", "        if i % 200 == 0:\n", "            print(f\"Coût après {i} itérations: {reg_loss:.6f}\")\n", "    \n", "    # Graphique de l'évolution du coût\n", "    plt.figure(figsize=(10, 6))\n", "    plt.plot(loss_history)\n", "    plt.xlabel('Nombre d\\'itérations')\n", "    plt.ylabel('Log Loss (avec régularisation)')\n", "    plt.title('Évolution de la fonction de coût')\n", "    plt.grid(True)\n", "    plt.show()\n", "    \n", "    return W"]}, {"cell_type": "markdown", "id": "c7d8e9f0", "metadata": {}, "source": ["### 5. Entraînement du modèle non linéaire"]}, {"cell_type": "code", "execution_count": null, "id": "g1h2i3j4", "metadata": {}, "outputs": [], "source": ["#souhail ouargui\n", "# Entraînement du modèle\n", "print(\"Entraînement du modèle de régression logistique non linéaire...\")\n", "W = regression_logistique(X_train_poly, y_train, learning_rate=0.01, n_iter=10000, lambda_reg=0.01)\n", "\n", "print(f\"\\nParamètres finaux:\")\n", "feature_names = ['biais', 'gpa', 'gre', 'gpa^2', 'gre^2', 'gpa*gre']\n", "for i, (name, weight) in enumerate(zip(feature_names, W.ravel())):\n", "    print(f\"  {name}: {weight:.6f}\")"]}, {"cell_type": "markdown", "id": "k5l6m7n8", "metadata": {}, "source": ["### 6. Évaluation du modèle"]}, {"cell_type": "code", "execution_count": null, "id": "o9p0q1r2", "metadata": {}, "outputs": [], "source": ["#souhail ouargui\n", "# Prédictions sur l'ensemble d'entraînement\n", "y_pred_train = predict(X_train_poly, W)\n", "accuracy_train = accuracy_score(y_train, y_pred_train)\n", "print(f\"Accuracy sur l'ensemble d'entraînement: {accuracy_train:.3f}\")\n", "\n", "# Prédictions sur l'ensemble de test\n", "y_pred_test = predict(X_test_poly, W)\n", "accuracy_test = accuracy_score(y_test, y_pred_test)\n", "print(f\"Accuracy sur l'ensemble de test: {accuracy_test:.3f}\")\n", "\n", "# Métriques supplémentaires\n", "precision = precision_score(y_test, y_pred_test)\n", "recall = recall_score(y_test, y_pred_test)\n", "f1 = f1_score(y_test, y_pred_test)\n", "\n", "print(f\"\\nMétriques sur l'ensemble de test:\")\n", "print(f\"  - Précision: {precision:.3f}\")\n", "print(f\"  - Rappel: {recall:.3f}\")\n", "print(f\"  - F1-Score: {f1:.3f}\")"]}, {"cell_type": "code", "execution_count": null, "id": "s3t4u5v6", "metadata": {}, "outputs": [], "source": ["#souhail ouargui\n", "# <PERSON><PERSON> de confusion\n", "cm = confusion_matrix(y_test, y_pred_test)\n", "print(\"\\nMatrice de confusion:\")\n", "print(cm)\n", "\n", "# Visualisation de la matrice de confusion\n", "plt.figure(figsize=(8, 6))\n", "sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', \n", "            xticklabels=['Non admis', 'Admis'], \n", "            yticklabels=['Non admis', 'Admis'])\n", "plt.xlabel('Prédictions')\n", "plt.ylabel('Vraies valeurs')\n", "plt.title('<PERSON><PERSON> de confusion - Modèle non linéaire')\n", "plt.show()"]}, {"cell_type": "markdown", "id": "w7x8y9z0", "metadata": {}, "source": ["### 7. Visualisation de la frontière de décision non linéaire"]}, {"cell_type": "code", "execution_count": null, "id": "a1b2c3d4", "metadata": {}, "outputs": [], "source": ["#souhail ouargui\n", "# Fonction pour tracer la frontière de décision non linéaire\n", "def plot_nonlinear_decision_boundary(X_original, y, W, scaler, title=\"Frontière de décision non linéaire\"):\n", "    plt.figure(figsize=(12, 8))\n", "    \n", "    # C<PERSON>er une grille de points dans l'espace original\n", "    h = 0.02\n", "    x_min, x_max = X_original[:, 0].min() - 0.1, X_original[:, 0].max() + 0.1\n", "    y_min, y_max = X_original[:, 1].min() - 20, X_original[:, 1].max() + 20\n", "    xx, yy = np.meshgrid(np.arange(x_min, x_max, h),\n", "                         np.arange(y_min, y_max, h))\n", "    \n", "    # Transformer la grille\n", "    grid_points = np.c_[xx.ravel(), yy.ravel()]\n", "    grid_scaled = scaler.transform(grid_points)\n", "    grid_poly = create_polynomial_features(grid_scaled, degree=2)\n", "    \n", "    # Prédire sur la grille\n", "    Z = modele(grid_poly, W)\n", "    Z = Z.reshape(xx.shape)\n", "    \n", "    # Tracer la frontière de décision\n", "    plt.contour(xx, yy, Z, levels=[0.5], colors='orange', linewidths=3)\n", "    plt.contourf(xx, yy, Z, levels=50, alpha=0.3, cmap='RdYlBu')\n", "    \n", "    # Tracer les points de données\n", "    scatter = plt.scatter(X_original[:, 0], X_original[:, 1], c=y.ravel(), \n", "                         cmap='winter', edgecolors='black', alpha=0.7, s=50)\n", "    plt.colorbar(scatter, label='Admission (0=Non, 1=Oui)')\n", "    \n", "    plt.xlabel('GPA')\n", "    plt.ylabel('GRE')\n", "    plt.title(title)\n", "    plt.grid(True, alpha=0.3)\n", "    plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "e5f6g7h8", "metadata": {}, "outputs": [], "source": ["#souhail ouargui\n", "# Visualisation sur l'ensemble d'entraînement\n", "plot_nonlinear_decision_boundary(X_train, y_train, W, scaler, \n", "                                \"Frontière de décision non linéaire - Ensemble d'entraînement\")"]}, {"cell_type": "code", "execution_count": null, "id": "i9j0k1l2", "metadata": {}, "outputs": [], "source": ["#souhail ouargui\n", "# Visualisation sur l'ensemble de test\n", "plot_nonlinear_decision_boundary(X_test, y_test, W, scaler, \n", "                                \"Frontière de décision non linéaire - Ensemble de test\")"]}, {"cell_type": "markdown", "id": "m3n4o5p6", "metadata": {}, "source": ["### 8. Prédiction sur de nouveaux exemples"]}, {"cell_type": "code", "execution_count": null, "id": "q7r8s9t0", "metadata": {}, "outputs": [], "source": ["#souhail ouargui\n", "# Exemples de nouveaux étudiants (GPA, GRE)\n", "nouveaux_etudiants = np.array([\n", "    [3.5, 650],  # <PERSON>\n", "    [2.8, 500],  # <PERSON><PERSON><PERSON><PERSON> moyen\n", "    [3.9, 750],  # Excellent étudiant\n", "    [2.5, 450],  # <PERSON><PERSON><PERSON><PERSON> faible\n", "    [3.2, 600],  # Étudiant correct\n", "])\n", "\n", "# Normaliser et créer les features polynomiales\n", "nouveaux_etudiants_scaled = scaler.transform(nouveaux_etudiants)\n", "nouveaux_etudiants_poly = create_polynomial_features(nouveaux_etudiants_scaled, degree=2)\n", "\n", "# Prédictions\n", "probabilites = modele(nouveaux_etudiants_poly, W)\n", "predictions = predict(nouveaux_etudiants_poly, W)\n", "\n", "print(\"Prédictions pour de nouveaux étudiants (modèle non linéaire):\")\n", "print(\"GPA\\tGRE\\tProbabilité\\tPrédiction\")\n", "print(\"-\" * 45)\n", "for i, (etudiant, prob, pred) in enumerate(zip(nouveaux_etudiants, probabilites, predictions)):\n", "    gpa, gre = etudiant\n", "    admission = \"Admis\" if pred[0] else \"Non admis\"\n", "    print(f\"{gpa:.1f}\\t{gre:.0f}\\t{prob[0]:.3f}\\t\\t{admission}\")"]}, {"cell_type": "code", "execution_count": null, "id": "u1v2w3x4", "metadata": {}, "outputs": [], "source": ["#souhail ouargui\n", "# Visualisation des nouveaux exemples sur la frontière de décision\n", "plt.figure(figsize=(12, 8))\n", "\n", "# Tracer la frontière de décision avec les données de test\n", "h = 0.02\n", "x_min, x_max = X_test[:, 0].min() - 0.1, X_test[:, 0].max() + 0.1\n", "y_min, y_max = X_test[:, 1].min() - 20, X_test[:, 1].max() + 20\n", "xx, yy = np.meshgrid(np.arange(x_min, x_max, h),\n", "                     np.arange(y_min, y_max, h))\n", "\n", "grid_points = np.c_[xx.ravel(), yy.ravel()]\n", "grid_scaled = scaler.transform(grid_points)\n", "grid_poly = create_polynomial_features(grid_scaled, degree=2)\n", "Z = modele(grid_poly, W)\n", "Z = Z.reshape(xx.shape)\n", "\n", "plt.contour(xx, yy, Z, levels=[0.5], colors='orange', linewidths=3, \n", "           linestyles='--', label='Frontière de décision')\n", "plt.contourf(xx, yy, Z, levels=50, alpha=0.3, cmap='RdYlBu')\n", "\n", "# Tracer les données de test\n", "scatter1 = plt.scatter(X_test[:, 0], X_test[:, 1], c=y_test.ravel(), \n", "                      cmap='winter', alpha=0.6, s=50, label='Données de test')\n", "\n", "# Tracer les nouveaux exemples\n", "scatter2 = plt.scatter(nouveaux_etudiants[:, 0], nouveaux_etudiants[:, 1], \n", "                      c='red', s=200, marker='*', edgecolors='black', \n", "                      label='Nouveaux étudiants')\n", "\n", "plt.xlabel('GPA')\n", "plt.ylabel('GRE')\n", "plt.title('Prédictions sur de nouveaux exemples - Modèle non linéaire')\n", "plt.legend()\n", "plt.grid(True, alpha=0.3)\n", "plt.show()"]}, {"cell_type": "markdown", "id": "y5z6a7b8", "metadata": {}, "source": ["### 9. Comparaison avec le modèle linéaire et scikit-learn"]}, {"cell_type": "code", "execution_count": null, "id": "c9d0e1f2", "metadata": {}, "outputs": [], "source": ["#souhail ouargui\n", "# Comparaison avec un modèle linéaire simple\n", "from sklearn.linear_model import LogisticRegression\n", "from sklearn.preprocessing import PolynomialFeatures as SklearnPolyFeatures\n", "\n", "# Mo<PERSON><PERSON>le linéaire avec scikit-learn\n", "linear_model = LogisticRegression(random_state=42)\n", "linear_model.fit(X_train_scaled, y_train.ravel())\n", "y_pred_linear = linear_model.predict(X_test_scaled)\n", "accuracy_linear = accuracy_score(y_test, y_pred_linear)\n", "\n", "# Modèle polynomial avec scikit-learn\n", "poly_sklearn = SklearnPolyFeatures(degree=2, include_bias=True)\n", "X_train_poly_sklearn = poly_sklearn.fit_transform(X_train_scaled)\n", "X_test_poly_sklearn = poly_sklearn.transform(X_test_scaled)\n", "\n", "poly_model = LogisticRegression(random_state=42, C=100)  # C=100 pour moins de régularisation\n", "poly_model.fit(X_train_poly_sklearn, y_train.ravel())\n", "y_pred_poly_sklearn = poly_model.predict(X_test_poly_sklearn)\n", "accuracy_poly_sklearn = accuracy_score(y_test, y_pred_poly_sklearn)\n", "\n", "print(\"Comparaison des modèles:\")\n", "print(\"=\" * 50)\n", "print(f\"1. <PERSON><PERSON><PERSON><PERSON> lin<PERSON> (scikit-learn):\")\n", "print(f\"   - Accuracy: {accuracy_linear:.3f}\")\n", "\n", "print(f\"\\n2. Notre modèle non linéaire (from scratch):\")\n", "print(f\"   - Accuracy: {accuracy_test:.3f}\")\n", "print(f\"   - Features: {X_train_poly.shape[1]} (avec termes polynomiaux)\")\n", "\n", "print(f\"\\n3. <PERSON><PERSON><PERSON><PERSON> polynomial (scikit-learn):\")\n", "print(f\"   - Accuracy: {accuracy_poly_sklearn:.3f}\")\n", "print(f\"   - Features: {X_train_poly_sklearn.shape[1]}\")\n", "\n", "print(f\"\\nAméliorations:\")\n", "print(f\"   - Non linéaire vs Liné<PERSON>: +{accuracy_test - accuracy_linear:.3f}\")\n", "print(f\"   - Notre implémentation vs sklearn: {accuracy_test - accuracy_poly_sklearn:+.3f}\")"]}, {"cell_type": "markdown", "id": "g3h4i5j6", "metadata": {}, "source": ["### 10. Analyse des coefficients et interprétation"]}, {"cell_type": "code", "execution_count": null, "id": "k7l8m9n0", "metadata": {}, "outputs": [], "source": ["#souhail ouargui\n", "# Analyse des coefficients du modèle non linéaire\n", "print(\"=\" * 60)\n", "print(\"ANALYSE DU MODÈLE DE RÉGRESSION LOGISTIQUE NON LINÉAIRE\")\n", "print(\"=\" * 60)\n", "\n", "print(f\"\\nÉquation du modèle:\")\n", "print(f\"P(admission=1) = sigmoid({W[0][0]:.4f} + {W[1][0]:.4f}*gpa + {W[2][0]:.4f}*gre\")\n", "print(f\"                        + {W[3][0]:.4f}*gpa² + {W[4][0]:.4f}*gre² + {W[5][0]:.4f}*gpa*gre)\")\n", "\n", "print(f\"\\nInterprétation des coefficients:\")\n", "feature_names = ['<PERSON><PERSON><PERSON>', 'GP<PERSON> (linéaire)', 'GRE (linéaire)', 'GPA² (quadratique)', 'GRE² (quadratique)', 'GPA×GRE (interaction)']\n", "for i, (name, weight) in enumerate(zip(feature_names, W.ravel())):\n", "    if i == 0:\n", "        print(f\"  {name}: {weight:.6f} (terme constant)\")\n", "    elif weight > 0:\n", "        print(f\"  {name}: +{weight:.6f} (effet positif)\")\n", "    else:\n", "        print(f\"  {name}: {weight:.6f} (effet négatif)\")\n", "\n", "print(f\"\\nCaractéristiques de la frontière de décision:\")\n", "if W[3][0] != 0 or W[4][0] != 0 or W[5][0] != 0:\n", "    print(f\"  - Frontière NON LINÉAIRE (courbe)\")\n", "    if W[3][0] < 0 and W[4][0] < 0:\n", "        print(f\"  - Forme: Elliptique/Parabolique (termes quadratiques négatifs)\")\n", "    elif W[3][0] > 0 and W[4][0] > 0:\n", "        print(f\"  - Forme: Hyperbolique (termes quadratiques positifs)\")\n", "    else:\n", "        print(f\"  - Forme: Mixte (un terme quadratique positif, un négatif)\")\n", "else:\n", "    print(f\"  - Frontière LINÉAIRE (droite)\")\n", "\n", "print(f\"\\nPerformances finales:\")\n", "print(f\"  - Accuracy d'entraînement: {accuracy_train:.1%}\")\n", "print(f\"  - Accuracy de test: {accuracy_test:.1%}\")\n", "print(f\"  - Différence (overfitting): {accuracy_train - accuracy_test:.1%}\")\n", "\n", "if accuracy_train - accuracy_test > 0.05:\n", "    print(f\"  ⚠️  Possible overfitting détecté\")\n", "else:\n", "    print(f\"  ✅ Pas d'overfitting significatif\")"]}, {"cell_type": "markdown", "id": "o1p2q3r4", "metadata": {}, "source": ["### 11. Conc<PERSON>\n", "\n", "**Résumé de l'implémentation non linéaire:**\n", "\n", "1. **Mod<PERSON>le implémenté:** Régression logistique binaire non linéaire from scratch\n", "2. **Variables d'entrée:** GPA et GRE + leurs termes polynomiaux (x², xy)\n", "3. **Variable cible:** Admission (0 = Non admis, 1 = Admis)\n", "4. **Algorithme d'optimisation:** Descente de gradient avec régularisation L2\n", "5. **Fonction de coût:** Log-loss avec régularisation\n", "\n", "**Améliorations par rapport au modèle linéaire:**\n", "- ✅ **Frontière de décision courbe** au lieu de linéaire\n", "- ✅ **Meilleure capacité de modélisation** des relations complexes\n", "- ✅ **Features polynomiales** pour capturer les interactions\n", "- ✅ **Régularisation** pour éviter l'overfitting\n", "- ✅ **Scaling correct** (é<PERSON><PERSON> le data leakage)\n", "\n", "**Points techniques importants:**\n", "- Séparation train/test **avant** le scaling\n", "- Création manuelle des features polynomiales\n", "- Biais inclus dans la matrice des features\n", "- Régularisation L2 sur les poids (pas le biais)\n", "- Visualisation de la frontière courbe\n", "\n", "**Applications:**\n", "- Modélisation de relations non linéaires entre GPA/GRE et admission\n", "- Détection d'interactions entre les variables\n", "- Amélioration des prédictions par rapport au modèle linéaire\n", "\n", "Ce modèle démontre comment étendre la régression logistique linéaire vers des frontières de décision plus complexes tout en gardant l'interprétabilité des coefficients."]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.0"}}, "nbformat": 4, "nbformat_minor": 5}